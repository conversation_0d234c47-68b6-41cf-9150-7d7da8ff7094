#!/usr/bin/env python3
"""
Diagnostic script to identify and fix broadcast system issues.
This script will:
1. Check delivery personnel capacity tracking
2. Verify broadcast eligibility logic
3. Test order completion flow
4. Fix any inconsistencies found
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.firebase_db import get_data, set_data, delete_data
from src.utils.delivery_personnel_utils import (
    get_real_time_capacity,
    find_available_personnel_with_capacity_check,
    update_personnel_capacity_on_assignment
)
from src.data_storage import (
    load_delivery_personnel_data,
    load_delivery_personnel_availability_data
)
import datetime
import json

def diagnose_capacity_tracking():
    """Diagnose capacity tracking issues"""
    print("\n🔍 DIAGNOSING CAPACITY TRACKING SYSTEM")
    print("=" * 60)
    
    # Load all delivery personnel
    personnel_data = load_delivery_personnel_data()
    availability_data = load_delivery_personnel_availability_data()
    
    print(f"📊 Found {len(personnel_data)} delivery personnel")
    
    capacity_issues = []
    
    for personnel_id, data in personnel_data.items():
        print(f"\n👤 Personnel: {data.get('name')} ({personnel_id})")
        print(f"   Status: {data.get('status')}")
        print(f"   Verified: {data.get('is_verified')}")
        print(f"   Availability: {availability_data.get(personnel_id, 'unknown')}")
        print(f"   Service Areas: {data.get('service_areas', [])}")
        
        # Check capacity tracking
        real_time_capacity = get_real_time_capacity(personnel_id)
        cached_capacity = get_data(f"delivery_personnel_capacity_tracking/{personnel_id}")
        
        print(f"   Real-time capacity: {real_time_capacity}")
        print(f"   Cached capacity: {cached_capacity}")
        
        # Check for inconsistencies
        if cached_capacity:
            cached_count = cached_capacity.get('current_orders', 0)
            active_orders = cached_capacity.get('active_order_numbers', [])
            last_updated = cached_capacity.get('last_updated', 'Unknown')
            
            print(f"   Cached count: {cached_count}")
            print(f"   Active orders: {active_orders}")
            print(f"   Last updated: {last_updated}")
            
            # Check if cached count matches active orders list
            if len(active_orders) != cached_count:
                capacity_issues.append({
                    'personnel_id': personnel_id,
                    'issue': 'count_mismatch',
                    'cached_count': cached_count,
                    'actual_count': len(active_orders)
                })
                print(f"   ⚠️  ISSUE: Count mismatch - cached: {cached_count}, actual: {len(active_orders)}")
        
        # Check confirmed orders for this personnel
        confirmed_orders = get_data("confirmed_orders") or {}
        active_confirmed_orders = []
        
        for order_num, order_data in confirmed_orders.items():
            if order_data.get('assigned_to') == personnel_id:
                # Check both 'status' and 'delivery_status' fields for completion
                status = order_data.get('status', '')
                delivery_status = order_data.get('delivery_status', '')

                # Order is active if neither status field indicates completion
                is_completed = (
                    status in ['completed', 'cancelled', 'delivered'] or
                    delivery_status in ['completed', 'cancelled', 'delivered']
                )

                if not is_completed:
                    active_confirmed_orders.append(order_num)
        
        print(f"   Active confirmed orders: {active_confirmed_orders}")
        
        # Check if real-time capacity matches confirmed orders
        if len(active_confirmed_orders) != real_time_capacity:
            capacity_issues.append({
                'personnel_id': personnel_id,
                'issue': 'confirmed_orders_mismatch',
                'real_time_capacity': real_time_capacity,
                'confirmed_orders_count': len(active_confirmed_orders),
                'confirmed_orders': active_confirmed_orders
            })
            print(f"   ⚠️  ISSUE: Real-time capacity ({real_time_capacity}) doesn't match confirmed orders ({len(active_confirmed_orders)})")
    
    return capacity_issues

def test_broadcast_eligibility():
    """Test broadcast eligibility for all personnel"""
    print("\n🎯 TESTING BROADCAST ELIGIBILITY")
    print("=" * 60)
    
    # Test with a sample area ID
    test_area_id = "area_001"  # Use a common area ID
    
    # Get all areas from Firebase to find a valid one
    areas_data = get_data("areas") or {}
    if areas_data:
        test_area_id = list(areas_data.keys())[0]
        print(f"Using test area: {test_area_id}")
    
    # Find available personnel
    available_personnel = find_available_personnel_with_capacity_check(test_area_id)
    print(f"Available personnel for area {test_area_id}: {available_personnel}")
    
    # Check each personnel individually
    personnel_data = load_delivery_personnel_data()
    availability_data = load_delivery_personnel_availability_data()
    
    for personnel_id, data in personnel_data.items():
        print(f"\n👤 Checking {data.get('name')} ({personnel_id}):")
        
        # Check basic availability criteria
        is_verified = data.get('is_verified', False)
        status = data.get('status', 'offline')
        availability = availability_data.get(personnel_id, status)
        service_areas = data.get('service_areas', [])
        
        print(f"   Verified: {is_verified}")
        print(f"   Status: {status}")
        print(f"   Availability: {availability}")
        print(f"   Service areas: {service_areas}")
        print(f"   Can serve test area: {test_area_id in service_areas}")
        
        # Check capacity
        capacity = get_real_time_capacity(personnel_id)
        max_capacity = data.get('max_capacity', 5)
        print(f"   Capacity: {capacity}/{max_capacity}")
        
        # Determine if should be eligible
        should_be_eligible = (
            is_verified and
            status != 'offline' and
            availability == 'available' and
            test_area_id in service_areas and
            capacity < 5
        )
        
        is_in_available_list = personnel_id in available_personnel
        
        print(f"   Should be eligible: {should_be_eligible}")
        print(f"   Is in available list: {is_in_available_list}")
        
        if should_be_eligible != is_in_available_list:
            print(f"   ⚠️  MISMATCH: Expected {should_be_eligible}, got {is_in_available_list}")

def fix_capacity_issues(capacity_issues):
    """Fix identified capacity tracking issues"""
    print("\n🔧 FIXING CAPACITY ISSUES")
    print("=" * 60)
    
    if not capacity_issues:
        print("✅ No capacity issues found to fix")
        return
    
    for issue in capacity_issues:
        personnel_id = issue['personnel_id']
        issue_type = issue['issue']
        
        print(f"\n🔧 Fixing issue for personnel {personnel_id}: {issue_type}")
        
        if issue_type == 'count_mismatch':
            # Fix count mismatch by recalculating
            cached_data = get_data(f"delivery_personnel_capacity_tracking/{personnel_id}")
            if cached_data:
                active_orders = cached_data.get('active_order_numbers', [])
                correct_count = len(active_orders)
                
                cached_data['current_orders'] = correct_count
                cached_data['last_updated'] = datetime.datetime.now().isoformat()
                cached_data['fixed_at'] = datetime.datetime.now().isoformat()
                
                set_data(f"delivery_personnel_capacity_tracking/{personnel_id}", cached_data)
                print(f"   ✅ Fixed count mismatch: set to {correct_count}")
        
        elif issue_type == 'confirmed_orders_mismatch':
            # Recalculate capacity based on confirmed orders
            confirmed_orders = get_data("confirmed_orders") or {}
            active_orders = []
            
            for order_num, order_data in confirmed_orders.items():
                if order_data.get('assigned_to') == personnel_id:
                    # Check both 'status' and 'delivery_status' fields for completion
                    status = order_data.get('status', '')
                    delivery_status = order_data.get('delivery_status', '')

                    # Order is active if neither status field indicates completion
                    is_completed = (
                        status in ['completed', 'cancelled', 'delivered'] or
                        delivery_status in ['completed', 'cancelled', 'delivered']
                    )

                    if not is_completed:
                        active_orders.append(order_num)
            
            # Update capacity tracking
            capacity_data = {
                'current_orders': len(active_orders),
                'active_order_numbers': active_orders,
                'last_updated': datetime.datetime.now().isoformat(),
                'fixed_at': datetime.datetime.now().isoformat(),
                'fix_reason': 'confirmed_orders_mismatch'
            }
            
            set_data(f"delivery_personnel_capacity_tracking/{personnel_id}", capacity_data)
            print(f"   ✅ Fixed confirmed orders mismatch: set to {len(active_orders)} orders")

def main():
    """Main diagnostic function"""
    print("🚀 BROADCAST SYSTEM DIAGNOSTIC TOOL")
    print("=" * 60)
    
    try:
        # Step 1: Diagnose capacity tracking
        capacity_issues = diagnose_capacity_tracking()
        
        # Step 2: Test broadcast eligibility
        test_broadcast_eligibility()
        
        # Step 3: Fix issues
        if capacity_issues:
            print(f"\n⚠️  Found {len(capacity_issues)} capacity issues")
            fix_capacity_issues(capacity_issues)
        
        # Step 4: Re-test after fixes
        print("\n🔄 RE-TESTING AFTER FIXES")
        test_broadcast_eligibility()
        
        print("\n✅ DIAGNOSTIC COMPLETE")
        
    except Exception as e:
        print(f"❌ Error during diagnosis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
