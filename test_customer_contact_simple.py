#!/usr/bin/env python3
"""
Simple Test for Customer Contact Functionality
Tests the core functions without complex bot handler inspection.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_core_functionality():
    """Test the core customer contact functionality"""
    print("🔍 Testing core customer contact functionality...")
    
    try:
        # Test button visibility logic
        from src.bots.order_track_bot import should_show_customer_contact_buttons
        
        # Test case 1: Order completed but not confirmed - should show buttons
        order_data_completed = {
            'delivery_status': 'completed',
            'status': 'CONFIRMED'
        }
        
        result = should_show_customer_contact_buttons(order_data_completed)
        if result:
            print("✅ Button visibility logic works for completed orders")
        else:
            print("❌ Button visibility logic failed for completed orders")
            return False
        
        # Test case 2: Order customer confirmed - should not show buttons
        order_data_confirmed = {
            'delivery_status': 'customer_confirmed',
            'status': 'CUSTOMER_CONFIRMED'
        }
        
        result = should_show_customer_contact_buttons(order_data_confirmed)
        if not result:
            print("✅ Button visibility logic works for confirmed orders")
        else:
            print("❌ Button visibility logic failed for confirmed orders")
            return False
        
        # Test button creation
        from src.bots.order_track_bot import create_customer_contact_buttons
        
        order_number = "TEST_12345_20241230_001"
        markup = create_customer_contact_buttons(order_number)
        
        if markup and hasattr(markup, 'keyboard'):
            buttons = markup.keyboard
            if len(buttons) > 0 and len(buttons[0]) == 2:
                confirm_btn = buttons[0][0]
                contact_btn = buttons[0][1]
                
                # Check button texts and callback data
                if ("Confirm Order Received" in confirm_btn.text and 
                    "Contact Customer" in contact_btn.text and
                    f"manual_confirm_{order_number}" in confirm_btn.callback_data and 
                    f"contact_customer_{order_number}" in contact_btn.callback_data):
                    print("✅ Button creation works correctly")
                else:
                    print("❌ Button creation failed - incorrect text or callback data")
                    return False
            else:
                print("❌ Button creation failed - incorrect layout")
                return False
        else:
            print("❌ Button creation failed - no markup created")
            return False
        
        # Test customer contact details formatting
        from src.bots.order_track_bot import format_customer_contact_details
        
        test_order_data = {
            'restaurant_id': '1',
            'phone_number': '+251912345678',
            'delivery_name': 'John Doe',
            'delivery_location': 'Test Location',
            'delivery_gate': 'Main Gate',
            'subtotal': 150,
            'delivery_fee': 25,
            'created_at': '2024-12-30 14:30:00',
            'confirmed_at': '2024-12-30 14:35:00',
            'completed_at': '2024-12-30 15:00:00',
            'items': [
                {'name': 'Pizza Margherita', 'price': 120, 'quantity': 1},
                {'name': 'Coca Cola', 'price': 30, 'quantity': 1}
            ]
        }
        
        contact_message = format_customer_contact_details(order_number, test_order_data)
        
        # Check if essential information is included
        required_elements = [
            "CUSTOMER CONTACT DETAILS",
            order_number,
            test_order_data['phone_number'],
            test_order_data['delivery_name'],
            "Pizza Margherita",
            "Customer has not confirmed receipt"
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in contact_message:
                missing_elements.append(element)
        
        if not missing_elements:
            print("✅ Customer contact details formatting works correctly")
        else:
            print(f"❌ Customer contact details formatting failed - missing: {missing_elements}")
            return False
        
        print("🎉 All core functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing core functionality: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_firebase_integration():
    """Test Firebase integration"""
    print("\n🔥 Testing Firebase integration...")
    
    try:
        from src.firebase_db import get_data, set_data, delete_data
        
        # Test confirmed_orders collection access
        confirmed_orders = get_data("confirmed_orders")
        print("✅ Firebase confirmed_orders collection accessible")
        
        # Create and test order data
        test_order = {
            "order_number": "TEST_CONTACT_SIMPLE_001",
            "user_id": "test_user",
            "status": "CONFIRMED",
            "delivery_status": "completed",
            "phone_number": "+251963630623",
            "delivery_name": "Test Customer"
        }
        
        # Test setting and getting order data
        if set_data("confirmed_orders/TEST_CONTACT_SIMPLE_001", test_order):
            retrieved_order = get_data("confirmed_orders/TEST_CONTACT_SIMPLE_001")
            if retrieved_order and retrieved_order.get('delivery_status') == 'completed':
                print("✅ Firebase order data operations work correctly")
                
                # Clean up
                delete_data("confirmed_orders/TEST_CONTACT_SIMPLE_001")
                print("✅ Test data cleaned up")
                return True
            else:
                print("❌ Firebase data retrieval failed")
                return False
        else:
            print("❌ Firebase data storage failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing Firebase integration: {e}")
        return False

def main():
    """Run simple customer contact functionality tests"""
    print("🚀 Starting Simple Customer Contact Functionality Tests")
    print("=" * 60)
    
    tests = [
        test_core_functionality,
        test_firebase_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} failed")
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All customer contact functionality tests passed!")
        return True
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
