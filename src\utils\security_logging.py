"""
Security-focused logging utilities for the Wiz Aroma Delivery Bot.
Provides secure logging with sensitive data filtering and audit trails.
"""

import re
import logging
import json
import hashlib
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from functools import wraps

# Patterns for sensitive data that should be filtered from logs
SENSITIVE_PATTERNS = [
    # API tokens and keys
    r'\b\d{10}:[A-Za-z0-9_-]{35}\b',  # Telegram bot tokens
    r'sk-[A-Za-z0-9]{48}',             # OpenAI API keys
    r'xoxb-[0-9]{11}-[0-9]{11}-[A-Za-z0-9]{24}',  # Slack bot tokens
    
    # Passwords and secrets
    r'password["\']?\s*[:=]\s*["\'][^"\']{4,}["\']',
    r'secret["\']?\s*[:=]\s*["\'][^"\']{4,}["\']',
    r'key["\']?\s*[:=]\s*["\'][^"\']{4,}["\']',
    
    # Personal information
    r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',  # Credit card numbers
    r'\b\d{3}-\d{2}-\d{4}\b',                        # SSN format
    r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # Email addresses
    
    # Phone numbers (Ethiopian format)
    r'\b(\+251|0)[79]\d{8}\b',
    
    # Private keys
    r'-----BEGIN [A-Z ]+PRIVATE KEY-----.*?-----END [A-Z ]+PRIVATE KEY-----',
]

# Compiled patterns for performance
COMPILED_PATTERNS = [re.compile(pattern, re.IGNORECASE | re.DOTALL) for pattern in SENSITIVE_PATTERNS]

# Security event types
SECURITY_EVENTS = {
    'AUTH_SUCCESS': 'Authentication successful',
    'AUTH_FAILURE': 'Authentication failed',
    'ACCESS_DENIED': 'Access denied',
    'RATE_LIMIT': 'Rate limit exceeded',
    'SUSPICIOUS_ACTIVITY': 'Suspicious activity detected',
    'DATA_ACCESS': 'Sensitive data accessed',
    'ADMIN_ACTION': 'Administrative action performed',
    'CONFIG_CHANGE': 'Configuration changed',
    'ERROR_SECURITY': 'Security-related error',
}


class SecurityLogger:
    """Enhanced logger with security-focused features"""
    
    def __init__(self, name: str = 'security'):
        self.logger = logging.getLogger(name)
        self.setup_security_handler()
    
    def setup_security_handler(self):
        """Set up security-specific log handler"""
        # Create security log file handler
        security_handler = logging.FileHandler('logs/security.log', encoding='utf-8')
        security_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - SECURITY - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        security_handler.setFormatter(security_formatter)
        security_handler.addFilter(SensitiveDataFilter())
        
        self.logger.addHandler(security_handler)
        self.logger.setLevel(logging.INFO)
    
    def sanitize_message(self, message: str) -> str:
        """Remove sensitive data from log messages"""
        sanitized = str(message)
        
        for pattern in COMPILED_PATTERNS:
            sanitized = pattern.sub('[REDACTED]', sanitized)
        
        return sanitized
    
    def log_security_event(self, event_type: str, user_id: Optional[int] = None, 
                          details: Optional[Dict[str, Any]] = None, 
                          level: str = 'INFO'):
        """Log a security event with proper sanitization"""
        event_desc = SECURITY_EVENTS.get(event_type, event_type)
        
        # Create base message
        message_parts = [f"EVENT:{event_type}", f"DESC:{event_desc}"]
        
        if user_id:
            # Hash user ID for privacy while maintaining uniqueness
            user_hash = hashlib.sha256(str(user_id).encode()).hexdigest()[:8]
            message_parts.append(f"USER_HASH:{user_hash}")
        
        if details:
            # Sanitize details
            sanitized_details = {}
            for key, value in details.items():
                sanitized_key = self.sanitize_message(str(key))
                sanitized_value = self.sanitize_message(str(value))
                sanitized_details[sanitized_key] = sanitized_value
            
            message_parts.append(f"DETAILS:{json.dumps(sanitized_details)}")
        
        message = " | ".join(message_parts)
        
        # Log at appropriate level
        if level.upper() == 'ERROR':
            self.logger.error(message)
        elif level.upper() == 'WARNING':
            self.logger.warning(message)
        else:
            self.logger.info(message)
    
    def log_auth_success(self, user_id: int, auth_type: str = 'general'):
        """Log successful authentication"""
        self.log_security_event('AUTH_SUCCESS', user_id, {'auth_type': auth_type})
    
    def log_auth_failure(self, user_id: int, reason: str = 'invalid_credentials'):
        """Log failed authentication"""
        self.log_security_event('AUTH_FAILURE', user_id, {'reason': reason}, 'WARNING')
    
    def log_access_denied(self, user_id: int, resource: str, reason: str = 'insufficient_permissions'):
        """Log access denied events"""
        self.log_security_event('ACCESS_DENIED', user_id, 
                               {'resource': resource, 'reason': reason}, 'WARNING')
    
    def log_rate_limit(self, user_id: int, action: str, limit_type: str = 'default'):
        """Log rate limit violations"""
        self.log_security_event('RATE_LIMIT', user_id, 
                               {'action': action, 'limit_type': limit_type}, 'WARNING')
    
    def log_suspicious_activity(self, user_id: Optional[int], activity: str, details: Dict[str, Any]):
        """Log suspicious activity"""
        self.log_security_event('SUSPICIOUS_ACTIVITY', user_id, 
                               {'activity': activity, **details}, 'ERROR')
    
    def log_data_access(self, user_id: int, data_type: str, operation: str):
        """Log access to sensitive data"""
        self.log_security_event('DATA_ACCESS', user_id, 
                               {'data_type': data_type, 'operation': operation})
    
    def log_admin_action(self, user_id: int, action: str, target: Optional[str] = None):
        """Log administrative actions"""
        details = {'action': action}
        if target:
            details['target'] = target
        self.log_security_event('ADMIN_ACTION', user_id, details)
    
    def log_config_change(self, user_id: int, config_type: str, change_type: str):
        """Log configuration changes"""
        self.log_security_event('CONFIG_CHANGE', user_id, 
                               {'config_type': config_type, 'change_type': change_type})


class SensitiveDataFilter(logging.Filter):
    """Filter to remove sensitive data from log records"""
    
    def filter(self, record):
        """Filter sensitive data from log record"""
        if hasattr(record, 'msg'):
            record.msg = self.sanitize_message(str(record.msg))
        
        if hasattr(record, 'args') and record.args:
            sanitized_args = []
            for arg in record.args:
                sanitized_args.append(self.sanitize_message(str(arg)))
            record.args = tuple(sanitized_args)
        
        return True
    
    def sanitize_message(self, message: str) -> str:
        """Remove sensitive data from message"""
        sanitized = str(message)
        
        for pattern in COMPILED_PATTERNS:
            sanitized = pattern.sub('[REDACTED]', sanitized)
        
        return sanitized


def security_audit_log(event_type: str):
    """Decorator to automatically log security events for functions"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            security_logger = SecurityLogger()
            
            # Extract user_id if available
            user_id = None
            if args and hasattr(args[0], 'from_user'):
                user_id = args[0].from_user.id
            elif 'user_id' in kwargs:
                user_id = kwargs['user_id']
            
            try:
                result = func(*args, **kwargs)
                security_logger.log_security_event(event_type, user_id, 
                                                 {'function': func.__name__, 'status': 'success'})
                return result
            except Exception as e:
                security_logger.log_security_event('ERROR_SECURITY', user_id, 
                                                 {'function': func.__name__, 'error': str(e)}, 'ERROR')
                raise
        
        return wrapper
    return decorator


def log_sensitive_operation(operation: str, user_id: int, details: Optional[Dict[str, Any]] = None):
    """Log sensitive operations with proper security context"""
    security_logger = SecurityLogger()
    security_logger.log_data_access(user_id, operation, 'sensitive_operation')
    
    if details:
        security_logger.log_security_event('DATA_ACCESS', user_id, 
                                         {'operation': operation, **details})


def create_audit_trail(user_id: int, action: str, resource: str, 
                      old_value: Any = None, new_value: Any = None):
    """Create detailed audit trail for important changes"""
    security_logger = SecurityLogger()
    
    audit_details = {
        'action': action,
        'resource': resource,
        'timestamp': datetime.utcnow().isoformat()
    }
    
    if old_value is not None:
        audit_details['old_value'] = str(old_value)[:100]  # Limit length
    
    if new_value is not None:
        audit_details['new_value'] = str(new_value)[:100]  # Limit length
    
    security_logger.log_admin_action(user_id, 'audit_trail', json.dumps(audit_details))


# Global security logger instance
security_logger = SecurityLogger()

# Export commonly used functions
__all__ = [
    'SecurityLogger',
    'security_logger',
    'security_audit_log',
    'log_sensitive_operation',
    'create_audit_trail',
    'SensitiveDataFilter'
]
