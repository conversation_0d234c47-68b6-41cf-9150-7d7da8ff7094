#!/usr/bin/env python3
"""
Demo: Manual Confirmation Enhancement
Demonstrates the complete manual confirmation workflow with customer notifications.
"""

import sys
import os
import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def demo_complete_manual_confirmation_workflow():
    """Demonstrate the complete enhanced manual confirmation workflow"""
    print("🎬 Demo: Enhanced Manual Confirmation Workflow")
    print("=" * 60)
    
    try:
        from src.bots.order_track_bot import (
            send_manual_confirmation_notification_to_customer,
            get_delivery_person_info_for_notification,
            format_manual_confirmation_customer_message,
            remove_customer_confirmation_buttons
        )
        from src.firebase_db import set_data, get_data, delete_data
        
        # Create demo order scenario
        demo_order_number = "DEMO_MANUAL_12345_20241230_001"
        demo_order_data = {
            'order_number': demo_order_number,
            'user_id': '987654321',  # Customer ID
            'restaurant_id': '1',
            'phone_number': '+251923456789',
            'delivery_name': '<PERSON>',
            'delivery_location': 'Addis Ababa University',
            'delivery_gate': 'Science Faculty Gate',
            'subtotal': 140,
            'delivery_fee': 30,
            'created_at': '2024-12-30 18:00:00',
            'confirmed_at': '2024-12-30 18:05:00',
            'assigned_to': 'delivery_004',
            'assigned_at': '2024-12-30 18:10:00',
            'completed_at': '2024-12-30 18:45:00',
            'status': 'CONFIRMED',
            'delivery_status': 'completed',  # Delivery completed but customer not responding
            'items': [
                {'name': 'Chicken Shawarma', 'price': 120, 'quantity': 1},
                {'name': 'Fresh Juice', 'price': 20, 'quantity': 1}
            ]
        }
        
        print("📋 Scenario: Customer not responding to delivery confirmation")
        print(f"   Order: {demo_order_number}")
        print(f"   Customer: {demo_order_data['delivery_name']} ({demo_order_data['user_id']})")
        print(f"   Status: Delivery completed but customer hasn't confirmed")
        
        # Step 1: Store demo order
        print("\n📋 Step 1: Creating demo order in Firebase...")
        set_data(f"confirmed_orders/{demo_order_number}", demo_order_data)
        print("✅ Demo order created")
        
        # Step 2: Get delivery person information
        print("\n👤 Step 2: Retrieving delivery person information...")
        delivery_info = get_delivery_person_info_for_notification(demo_order_data)
        print(f"✅ Delivery person info: {delivery_info}")
        
        # Step 3: Format customer notification message
        print("\n📝 Step 3: Formatting customer notification message...")
        customer_message = format_manual_confirmation_customer_message(
            demo_order_number,
            demo_order_data,
            delivery_info
        )
        
        print("✅ Customer notification message formatted:")
        print("-" * 50)
        print(customer_message[:500] + "..." if len(customer_message) > 500 else customer_message)
        print("-" * 50)
        
        # Step 4: Simulate button removal
        print("\n🔄 Step 4: Simulating customer button removal...")
        # Create mock payment message data
        mock_payment_data = {
            'chat_id': demo_order_data['user_id'],
            'message_id': 888888,  # Mock message ID
            'message_text': 'Mock payment confirmation message',
            'timestamp': datetime.datetime.now().isoformat()
        }
        set_data(f"user_payment_messages/{demo_order_number}", mock_payment_data)
        
        # Attempt button removal (will fail gracefully with mock data)
        remove_customer_confirmation_buttons(demo_order_number, demo_order_data['user_id'])
        print("✅ Button removal attempted (graceful failure expected with mock data)")
        
        # Step 5: Simulate customer notification
        print("\n📱 Step 5: Simulating customer notification...")
        admin_user_id = 7729984017  # Admin who performed manual confirmation
        
        # This will attempt to send but fail gracefully with mock customer ID
        send_manual_confirmation_notification_to_customer(
            demo_order_number,
            demo_order_data,
            admin_user_id
        )
        print("✅ Customer notification attempted (graceful failure expected with mock customer)")
        
        # Step 6: Verify order status update
        print("\n📊 Step 6: Simulating order status update...")
        demo_order_data['status'] = 'CUSTOMER_CONFIRMED'
        demo_order_data['delivery_status'] = 'customer_confirmed'
        demo_order_data['customer_confirmed_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        demo_order_data['manually_confirmed_by'] = admin_user_id
        demo_order_data['manual_confirmation'] = True
        
        set_data(f"confirmed_orders/{demo_order_number}", demo_order_data)
        
        # Verify the update
        updated_order = get_data(f"confirmed_orders/{demo_order_number}")
        if updated_order and updated_order.get('manual_confirmation'):
            print("✅ Order status updated with manual confirmation metadata:")
            print(f"   - Manually confirmed by: {updated_order.get('manually_confirmed_by')}")
            print(f"   - Confirmed at: {updated_order.get('customer_confirmed_at')}")
            print(f"   - Manual flag: {updated_order.get('manual_confirmation')}")
        else:
            print("❌ Order status update failed")
            return False
        
        # Clean up demo data
        print("\n🧹 Step 7: Cleaning up demo data...")
        delete_data(f"confirmed_orders/{demo_order_number}")
        delete_data(f"user_payment_messages/{demo_order_number}")
        print("✅ Demo data cleaned up")
        
        print("\n🎉 Enhanced manual confirmation workflow demo completed!")
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_error_handling_scenarios():
    """Demonstrate error handling in various scenarios"""
    print("\n⚠️ Demo: Error Handling Scenarios")
    print("=" * 60)
    
    try:
        from src.bots.order_track_bot import (
            get_delivery_person_info_for_notification,
            format_manual_confirmation_customer_message,
            send_manual_confirmation_notification_to_customer
        )
        
        print("📋 Scenario 1: Missing delivery person information")
        order_no_delivery = {'assigned_to': 'nonexistent_delivery_person'}
        info = get_delivery_person_info_for_notification(order_no_delivery)
        print(f"✅ Fallback info provided: {info}")
        
        print("\n📋 Scenario 2: Minimal order data")
        minimal_order = {'subtotal': 50}
        minimal_delivery_info = {'name': 'Unknown Driver', 'found': False}
        
        message = format_manual_confirmation_customer_message(
            "MINIMAL_001",
            minimal_order,
            minimal_delivery_info
        )
        
        if "Order #MINIMAL_001" in message and "0909782606" in message:
            print("✅ Minimal data handled correctly with fallback contact info")
        else:
            print("❌ Minimal data handling failed")
            return False
        
        print("\n📋 Scenario 3: Invalid customer notification")
        try:
            send_manual_confirmation_notification_to_customer("INVALID_ORDER", {}, 0)
            print("✅ Invalid notification handled gracefully")
        except Exception as e:
            print(f"✅ Exception handled: {e}")
        
        print("\n🎉 Error handling scenarios completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error handling demo failed: {e}")
        return False

def demo_message_content_analysis():
    """Analyze the content of customer notification messages"""
    print("\n📄 Demo: Customer Message Content Analysis")
    print("=" * 60)
    
    try:
        from src.bots.order_track_bot import format_manual_confirmation_customer_message
        
        # Test different scenarios
        scenarios = [
            {
                'name': 'Complete Order with Known Driver',
                'order_data': {
                    'restaurant_id': '1',
                    'subtotal': 200,
                    'delivery_fee': 40,
                    'completed_at': '2024-12-30 19:30:00'
                },
                'delivery_info': {
                    'name': 'Ahmed Hassan',
                    'phone': '+251934567890',
                    'found': True
                }
            },
            {
                'name': 'Order with Unknown Driver',
                'order_data': {
                    'restaurant_id': '2',
                    'subtotal': 80,
                    'delivery_fee': 15,
                    'completed_at': '2024-12-30 20:00:00'
                },
                'delivery_info': {
                    'name': 'Our delivery team',
                    'phone': 'N/A',
                    'found': False
                }
            }
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n📋 Scenario {i}: {scenario['name']}")
            
            message = format_manual_confirmation_customer_message(
                f"ANALYSIS_{i:03d}",
                scenario['order_data'],
                scenario['delivery_info']
            )
            
            # Analyze message content
            analysis = {
                'Length': len(message),
                'Has Order Number': f"ANALYSIS_{i:03d}" in message,
                'Has Total Amount': 'Birr' in message,
                'Has Contact Info': '0909782606' in message,
                'Has Email': '<EMAIL>' in message,
                'Has Delivery Person': scenario['delivery_info']['name'] in message,
                'Professional Tone': 'administrative' in message.lower()
            }
            
            print("   Analysis Results:")
            for key, value in analysis.items():
                status = "✅" if value else "❌"
                print(f"   {status} {key}: {value}")
            
            # Check message structure
            required_sections = [
                'Order Completed - Administrative Confirmation',
                'Status Update:',
                'Order Process Complete',
                'Need Help?'
            ]
            
            missing_sections = [section for section in required_sections if section not in message]
            if not missing_sections:
                print("   ✅ All required sections present")
            else:
                print(f"   ❌ Missing sections: {missing_sections}")
        
        print("\n🎉 Message content analysis completed!")
        return True
        
    except Exception as e:
        print(f"❌ Message content analysis failed: {e}")
        return False

def main():
    """Run all manual confirmation enhancement demos"""
    print("🎬 Manual Confirmation Enhancement Demo Suite")
    print("=" * 60)
    
    demos = [
        demo_complete_manual_confirmation_workflow,
        demo_error_handling_scenarios,
        demo_message_content_analysis
    ]
    
    passed = 0
    total = len(demos)
    
    for demo in demos:
        try:
            if demo():
                passed += 1
            else:
                print(f"❌ Demo {demo.__name__} failed")
        except Exception as e:
            print(f"❌ Demo {demo.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Demo Results: {passed}/{total} demos completed successfully")
    
    if passed == total:
        print("🎉 All manual confirmation enhancement demos completed!")
        print("\n📝 Enhancement Summary:")
        print("   ✅ Customer notification system implemented")
        print("   ✅ Button removal functionality added")
        print("   ✅ Delivery person information integration")
        print("   ✅ Professional customer messaging")
        print("   ✅ Contact center information included")
        print("   ✅ Error handling and fallbacks implemented")
        print("   ✅ Integration with existing manual confirmation")
        print("\n🚀 Manual confirmation enhancement is production-ready!")
        return True
    else:
        print("⚠️ Some demos failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
