#!/usr/bin/env python3
"""
Security Audit Script for Wiz-Aroma Project
Scans for security vulnerabilities and sensitive data exposure.
"""

import os
import re
import json
import logging
from pathlib import Path
from typing import List, Dict, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

# Patterns to detect sensitive data
SENSITIVE_PATTERNS = {
    'api_tokens': [
        r'\b\d{10}:[A-Za-z0-9_-]{35}\b',  # Telegram bot tokens
        r'sk-[A-Za-z0-9]{48}',             # OpenAI API keys
        r'xoxb-[0-9]{11}-[0-9]{11}-[A-Za-z0-9]{24}',  # Slack bot tokens
    ],
    'credentials': [
        r'password\s*[:=]\s*["\'][^"\']{8,}["\']',
        r'secret\s*[:=]\s*["\'][^"\']{8,}["\']',
        r'key\s*[:=]\s*["\'][^"\']{8,}["\']',
        r'token\s*[:=]\s*["\'][^"\']{8,}["\']',
    ],
    'private_keys': [
        r'-----BEGIN PRIVATE KEY-----',
        r'-----BEGIN RSA PRIVATE KEY-----',
        r'-----BEGIN EC PRIVATE KEY-----',
    ],
    'database_urls': [
        r'mongodb://[^/\s]+',
        r'postgres://[^/\s]+',
        r'mysql://[^/\s]+',
        r'https://[^/\s]+-default-rtdb\.firebaseio\.com',
    ],
    'email_passwords': [
        r'[a-z]{4}\s+[a-z]{4}\s+[a-z]{4}\s+[a-z]{4}',  # App-specific passwords
    ]
}

# Files to exclude from scanning
EXCLUDE_PATTERNS = [
    r'\.git/',
    r'__pycache__/',
    r'\.pyc$',
    r'node_modules/',
    r'\.log$',
    r'\.backup$',
    r'security_audit\.py$',
]

# Files that should never contain sensitive data
CRITICAL_FILES = [
    '.gitignore',
    'README.md',
    'requirements.txt',
    'setup.py',
    'Dockerfile',
]


class SecurityAudit:
    def __init__(self, project_root: str = '.'):
        self.project_root = Path(project_root)
        self.findings: List[Dict] = []
        self.stats = {
            'files_scanned': 0,
            'sensitive_files_found': 0,
            'total_issues': 0
        }

    def should_exclude_file(self, file_path: Path) -> bool:
        """Check if file should be excluded from scanning"""
        file_str = str(file_path)
        return any(re.search(pattern, file_str) for pattern in EXCLUDE_PATTERNS)

    def scan_file_for_patterns(self, file_path: Path) -> List[Dict]:
        """Scan a single file for sensitive patterns"""
        findings = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            for category, patterns in SENSITIVE_PATTERNS.items():
                for pattern in patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
                    for match in matches:
                        # Get line number
                        line_num = content[:match.start()].count('\n') + 1
                        
                        findings.append({
                            'file': str(file_path),
                            'category': category,
                            'pattern': pattern,
                            'line': line_num,
                            'match': match.group()[:50] + '...' if len(match.group()) > 50 else match.group(),
                            'severity': self.get_severity(category, file_path)
                        })
                        
        except Exception as e:
            logger.warning(f"Error scanning {file_path}: {e}")
            
        return findings

    def get_severity(self, category: str, file_path: Path) -> str:
        """Determine severity based on category and file location"""
        if str(file_path.name) in CRITICAL_FILES:
            return 'CRITICAL'
        elif category in ['private_keys', 'api_tokens']:
            return 'HIGH'
        elif category in ['credentials', 'database_urls']:
            return 'MEDIUM'
        else:
            return 'LOW'

    def check_file_permissions(self) -> List[Dict]:
        """Check for files with overly permissive permissions"""
        findings = []
        
        for file_path in self.project_root.rglob('*'):
            if file_path.is_file() and not self.should_exclude_file(file_path):
                try:
                    # Check if file is world-readable (on Unix systems)
                    if hasattr(os, 'stat') and os.name != 'nt':
                        stat_info = file_path.stat()
                        if stat_info.st_mode & 0o044:  # World or group readable
                            if any(sensitive in str(file_path).lower() 
                                  for sensitive in ['key', 'secret', 'password', 'token']):
                                findings.append({
                                    'file': str(file_path),
                                    'category': 'file_permissions',
                                    'issue': 'Sensitive file is world/group readable',
                                    'severity': 'HIGH'
                                })
                except Exception as e:
                    logger.debug(f"Error checking permissions for {file_path}: {e}")
                    
        return findings

    def check_gitignore(self) -> List[Dict]:
        """Check if .gitignore properly excludes sensitive files"""
        findings = []
        gitignore_path = self.project_root / '.gitignore'
        
        if not gitignore_path.exists():
            findings.append({
                'file': '.gitignore',
                'category': 'configuration',
                'issue': '.gitignore file missing',
                'severity': 'HIGH'
            })
            return findings
        
        try:
            with open(gitignore_path, 'r') as f:
                gitignore_content = f.read()
            
            required_patterns = [
                '.env',
                '*.key',
                '*.pem',
                '*credentials*.json',
                'secrets.json'
            ]
            
            for pattern in required_patterns:
                if pattern not in gitignore_content:
                    findings.append({
                        'file': '.gitignore',
                        'category': 'configuration',
                        'issue': f'Missing pattern: {pattern}',
                        'severity': 'MEDIUM'
                    })
                    
        except Exception as e:
            logger.error(f"Error reading .gitignore: {e}")
            
        return findings

    def scan_project(self) -> Dict:
        """Perform complete security scan of the project"""
        logger.info(f"Starting security audit of {self.project_root}")
        
        # Scan all files for sensitive patterns
        for file_path in self.project_root.rglob('*'):
            if file_path.is_file() and not self.should_exclude_file(file_path):
                self.stats['files_scanned'] += 1
                file_findings = self.scan_file_for_patterns(file_path)
                if file_findings:
                    self.stats['sensitive_files_found'] += 1
                self.findings.extend(file_findings)
        
        # Check file permissions
        permission_findings = self.check_file_permissions()
        self.findings.extend(permission_findings)
        
        # Check .gitignore configuration
        gitignore_findings = self.check_gitignore()
        self.findings.extend(gitignore_findings)
        
        self.stats['total_issues'] = len(self.findings)
        
        return self.generate_report()

    def generate_report(self) -> Dict:
        """Generate security audit report"""
        # Group findings by severity
        by_severity = {'CRITICAL': [], 'HIGH': [], 'MEDIUM': [], 'LOW': []}
        for finding in self.findings:
            severity = finding.get('severity', 'LOW')
            by_severity[severity].append(finding)
        
        report = {
            'summary': {
                'total_files_scanned': self.stats['files_scanned'],
                'files_with_issues': self.stats['sensitive_files_found'],
                'total_issues': self.stats['total_issues'],
                'critical_issues': len(by_severity['CRITICAL']),
                'high_issues': len(by_severity['HIGH']),
                'medium_issues': len(by_severity['MEDIUM']),
                'low_issues': len(by_severity['LOW'])
            },
            'findings_by_severity': by_severity,
            'recommendations': self.get_recommendations()
        }
        
        return report

    def get_recommendations(self) -> List[str]:
        """Get security recommendations based on findings"""
        recommendations = []
        
        if any(f['severity'] == 'CRITICAL' for f in self.findings):
            recommendations.append("🚨 IMMEDIATE ACTION REQUIRED: Critical security issues found")
            recommendations.append("Remove all sensitive data from repository immediately")
            recommendations.append("Rotate all exposed credentials and API tokens")
        
        if any(f['category'] == 'api_tokens' for f in self.findings):
            recommendations.append("Move all API tokens to environment variables")
            recommendations.append("Add .env to .gitignore if not already present")
        
        if any(f['category'] == 'private_keys' for f in self.findings):
            recommendations.append("Remove private keys from repository")
            recommendations.append("Store private keys securely outside the repository")
        
        if any(f['category'] == 'configuration' for f in self.findings):
            recommendations.append("Update .gitignore to exclude sensitive files")
            recommendations.append("Review repository configuration for security")
        
        recommendations.extend([
            "Implement proper secrets management",
            "Use environment variables for all sensitive configuration",
            "Enable branch protection rules",
            "Set up security scanning in CI/CD pipeline",
            "Regular security audits (monthly recommended)"
        ])
        
        return recommendations

    def print_report(self, report: Dict):
        """Print formatted security report"""
        print("\n" + "="*80)
        print("🔐 SECURITY AUDIT REPORT")
        print("="*80)
        
        summary = report['summary']
        print(f"\n📊 SUMMARY:")
        print(f"   Files Scanned: {summary['total_files_scanned']}")
        print(f"   Files with Issues: {summary['files_with_issues']}")
        print(f"   Total Issues: {summary['total_issues']}")
        print(f"   Critical: {summary['critical_issues']}")
        print(f"   High: {summary['high_issues']}")
        print(f"   Medium: {summary['medium_issues']}")
        print(f"   Low: {summary['low_issues']}")
        
        # Print findings by severity
        for severity in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']:
            findings = report['findings_by_severity'][severity]
            if findings:
                print(f"\n🚨 {severity} SEVERITY ISSUES:")
                for finding in findings:
                    print(f"   📁 {finding['file']}")
                    if 'line' in finding:
                        print(f"      Line {finding['line']}: {finding.get('match', finding.get('issue', ''))}")
                    else:
                        print(f"      Issue: {finding.get('issue', '')}")
        
        # Print recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"   {i}. {rec}")
        
        print("\n" + "="*80)


def main():
    """Main function to run security audit"""
    audit = SecurityAudit()
    report = audit.scan_project()
    audit.print_report(report)
    
    # Save report to file
    with open('security_audit_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Detailed report saved to: security_audit_report.json")
    
    # Exit with error code if critical issues found
    if report['summary']['critical_issues'] > 0:
        print("\n🚨 CRITICAL SECURITY ISSUES FOUND - IMMEDIATE ACTION REQUIRED!")
        exit(1)
    elif report['summary']['high_issues'] > 0:
        print("\n⚠️  HIGH SEVERITY ISSUES FOUND - ACTION RECOMMENDED")
        exit(2)


if __name__ == "__main__":
    main()
