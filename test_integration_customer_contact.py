#!/usr/bin/env python3
"""
Integration Test for Customer Contact Functionality
Tests integration with existing order tracking system.
"""

import sys
import os
import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_end_to_end_order_flow():
    """Test complete order flow with customer contact functionality"""
    print("🔄 Testing End-to-End Order Flow with Customer Contact")
    print("=" * 60)
    
    try:
        from src.bots.order_track_bot import (
            send_detailed_order_notification,
            notify_delivery_assignment,
            notify_delivery_accepted,
            notify_delivery_completed,
            notify_customer_confirmed,
            should_show_customer_contact_buttons,
            format_customer_contact_details
        )
        from src.firebase_db import set_data, get_data, delete_data
        
        # Create test order
        test_order_number = "E2E_12345_20241230_001"
        test_order_data = {
            'order_number': test_order_number,
            'user_id': '111222333',
            'restaurant_id': '1',
            'phone_number': '+251945678901',
            'delivery_name': 'Test Customer',
            'delivery_location': 'Test University',
            'delivery_gate': 'Main Entrance',
            'subtotal': 160,
            'delivery_fee': 28,
            'created_at': '2024-12-30 16:00:00',
            'confirmed_at': '2024-12-30 16:05:00',
            'status': 'CONFIRMED',
            'delivery_status': 'pending_assignment',
            'items': [
                {'name': 'Chicken Pizza', 'price': 140, 'quantity': 1},
                {'name': 'Water', 'price': 20, 'quantity': 1}
            ]
        }
        
        print("📋 Step 1: Creating initial order...")
        set_data(f"confirmed_orders/{test_order_number}", test_order_data)
        
        # Test initial state - no buttons should show
        should_show_initial = should_show_customer_contact_buttons(test_order_data)
        print(f"✅ Initial state - Show buttons: {should_show_initial} (Expected: False)")
        
        # Step 2: Order assignment
        print("\n📋 Step 2: Simulating order assignment...")
        test_order_data['delivery_status'] = 'assigned'
        test_order_data['assigned_to'] = 'delivery_003'
        test_order_data['assigned_at'] = '2024-12-30 16:10:00'
        set_data(f"confirmed_orders/{test_order_number}", test_order_data)
        
        should_show_assigned = should_show_customer_contact_buttons(test_order_data)
        print(f"✅ Assigned state - Show buttons: {should_show_assigned} (Expected: False)")
        
        # Step 3: Order accepted
        print("\n📋 Step 3: Simulating order acceptance...")
        test_order_data['delivery_status'] = 'accepted'
        test_order_data['accepted_at'] = '2024-12-30 16:12:00'
        set_data(f"confirmed_orders/{test_order_number}", test_order_data)
        
        should_show_accepted = should_show_customer_contact_buttons(test_order_data)
        print(f"✅ Accepted state - Show buttons: {should_show_accepted} (Expected: False)")
        
        # Step 4: Order in transit
        print("\n📋 Step 4: Simulating order in transit...")
        test_order_data['delivery_status'] = 'in_transit'
        test_order_data['in_transit_at'] = '2024-12-30 16:20:00'
        set_data(f"confirmed_orders/{test_order_number}", test_order_data)
        
        should_show_transit = should_show_customer_contact_buttons(test_order_data)
        print(f"✅ In transit state - Show buttons: {should_show_transit} (Expected: False)")
        
        # Step 5: Order completed - BUTTONS SHOULD APPEAR
        print("\n📋 Step 5: Simulating order completion...")
        test_order_data['delivery_status'] = 'completed'
        test_order_data['completed_at'] = '2024-12-30 16:35:00'
        set_data(f"confirmed_orders/{test_order_number}", test_order_data)
        
        should_show_completed = should_show_customer_contact_buttons(test_order_data)
        print(f"✅ Completed state - Show buttons: {should_show_completed} (Expected: True)")
        
        if should_show_completed:
            # Test customer contact details at this stage
            contact_details = format_customer_contact_details(test_order_number, test_order_data)
            if "Customer has not confirmed receipt" in contact_details:
                print("✅ Customer contact details correctly formatted for completed order")
            else:
                print("❌ Customer contact details missing issue description")
                return False
        else:
            print("❌ Buttons not showing for completed order")
            return False
        
        # Step 6: Customer confirmation - BUTTONS SHOULD DISAPPEAR
        print("\n📋 Step 6: Simulating customer confirmation...")
        test_order_data['status'] = 'CUSTOMER_CONFIRMED'
        test_order_data['delivery_status'] = 'customer_confirmed'
        test_order_data['customer_confirmed_at'] = '2024-12-30 16:40:00'
        set_data(f"confirmed_orders/{test_order_number}", test_order_data)
        
        should_show_confirmed = should_show_customer_contact_buttons(test_order_data)
        print(f"✅ Confirmed state - Show buttons: {should_show_confirmed} (Expected: False)")
        
        if not should_show_confirmed:
            print("✅ Buttons correctly hidden after customer confirmation")
        else:
            print("❌ Buttons still showing after customer confirmation")
            return False
        
        # Clean up
        print("\n🧹 Cleaning up test data...")
        delete_data(f"confirmed_orders/{test_order_number}")
        print("✅ Test data cleaned up")
        
        print("\n🎉 End-to-End order flow test passed!")
        return True
        
    except Exception as e:
        print(f"❌ End-to-End test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_confirmation_integration():
    """Test manual confirmation integration with existing system"""
    print("\n🔧 Testing Manual Confirmation Integration")
    print("=" * 60)
    
    try:
        from src.firebase_db import set_data, get_data, delete_data
        
        # Create order stuck at completion
        stuck_order_number = "STUCK_12345_20241230_001"
        stuck_order_data = {
            'order_number': stuck_order_number,
            'user_id': '444555666',
            'restaurant_id': '2',
            'phone_number': '+251956789012',
            'delivery_name': 'Stuck Customer',
            'delivery_location': 'Remote Location',
            'delivery_gate': 'Back Gate',
            'subtotal': 90,
            'delivery_fee': 20,
            'status': 'CONFIRMED',
            'delivery_status': 'completed',
            'completed_at': '2024-12-30 15:00:00',
            'items': [
                {'name': 'Sandwich', 'price': 70, 'quantity': 1},
                {'name': 'Tea', 'price': 20, 'quantity': 1}
            ]
        }
        
        print("📋 Step 1: Creating stuck order (customer not responding)...")
        set_data(f"confirmed_orders/{stuck_order_number}", stuck_order_data)
        
        # Verify order is in correct state for manual confirmation
        order_check = get_data(f"confirmed_orders/{stuck_order_number}")
        if order_check and order_check.get('delivery_status') == 'completed':
            print("✅ Order correctly in 'completed' state")
        else:
            print("❌ Order not in expected state")
            return False
        
        # Simulate manual confirmation process
        print("\n👤 Step 2: Simulating manual confirmation process...")
        
        # This simulates what happens when admin clicks "Confirm Order Received"
        stuck_order_data['status'] = 'CUSTOMER_CONFIRMED'
        stuck_order_data['delivery_status'] = 'customer_confirmed'
        stuck_order_data['customer_confirmed_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        stuck_order_data['manually_confirmed_by'] = 7729984017  # Admin ID
        stuck_order_data['manual_confirmation'] = True
        
        set_data(f"confirmed_orders/{stuck_order_number}", stuck_order_data)
        
        # Verify manual confirmation was recorded
        confirmed_order = get_data(f"confirmed_orders/{stuck_order_number}")
        if (confirmed_order and 
            confirmed_order.get('manual_confirmation') and
            confirmed_order.get('manually_confirmed_by') == 7729984017 and
            confirmed_order.get('delivery_status') == 'customer_confirmed'):
            print("✅ Manual confirmation properly recorded")
            print(f"   - Manual flag: {confirmed_order.get('manual_confirmation')}")
            print(f"   - Confirmed by: {confirmed_order.get('manually_confirmed_by')}")
            print(f"   - Status: {confirmed_order.get('delivery_status')}")
        else:
            print("❌ Manual confirmation not properly recorded")
            return False
        
        # Test that buttons would no longer show
        from src.bots.order_track_bot import should_show_customer_contact_buttons
        should_show_after_manual = should_show_customer_contact_buttons(confirmed_order)
        if not should_show_after_manual:
            print("✅ Buttons correctly hidden after manual confirmation")
        else:
            print("❌ Buttons still showing after manual confirmation")
            return False
        
        # Clean up
        print("\n🧹 Cleaning up test data...")
        delete_data(f"confirmed_orders/{stuck_order_number}")
        print("✅ Test data cleaned up")
        
        print("\n🎉 Manual confirmation integration test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Manual confirmation integration test failed: {e}")
        return False

def test_edge_cases():
    """Test edge cases and error conditions"""
    print("\n⚠️ Testing Edge Cases and Error Conditions")
    print("=" * 60)
    
    try:
        from src.bots.order_track_bot import (
            should_show_customer_contact_buttons,
            format_customer_contact_details
        )
        
        # Test case 1: Empty order data
        print("📋 Test 1: Empty order data...")
        empty_result = should_show_customer_contact_buttons({})
        print(f"✅ Empty data result: {empty_result} (Expected: False)")
        
        # Test case 2: Missing delivery_status
        print("\n📋 Test 2: Missing delivery_status field...")
        missing_status = should_show_customer_contact_buttons({'status': 'CONFIRMED'})
        print(f"✅ Missing status result: {missing_status} (Expected: False)")
        
        # Test case 3: Cancelled order
        print("\n📋 Test 3: Cancelled order...")
        cancelled_order = {
            'delivery_status': 'cancelled',
            'status': 'CANCELLED'
        }
        cancelled_result = should_show_customer_contact_buttons(cancelled_order)
        print(f"✅ Cancelled order result: {cancelled_result} (Expected: False)")
        
        # Test case 4: Error state order
        print("\n📋 Test 4: Error state order...")
        error_order = {
            'delivery_status': 'error',
            'status': 'ERROR'
        }
        error_result = should_show_customer_contact_buttons(error_order)
        print(f"✅ Error order result: {error_result} (Expected: False)")
        
        # Test case 5: Minimal order data for contact details
        print("\n📋 Test 5: Minimal order data for contact details...")
        minimal_order = {
            'phone_number': '+251900000000',
            'delivery_name': 'Minimal Customer'
        }
        minimal_contact = format_customer_contact_details("MIN_001", minimal_order)
        if "CUSTOMER CONTACT DETAILS" in minimal_contact and "+251900000000" in minimal_contact:
            print("✅ Minimal contact details formatted correctly")
        else:
            print("❌ Minimal contact details formatting failed")
            return False
        
        print("\n🎉 Edge cases test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Edge cases test failed: {e}")
        return False

def main():
    """Run all integration tests"""
    print("🧪 Customer Contact Functionality - Integration Tests")
    print("=" * 60)
    
    tests = [
        test_end_to_end_order_flow,
        test_manual_confirmation_integration,
        test_edge_cases
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} failed")
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Integration Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests passed!")
        print("\n✅ Customer contact functionality is fully integrated and ready!")
        print("\n📋 Summary of verified functionality:")
        print("   ✅ Button visibility logic works correctly throughout order lifecycle")
        print("   ✅ Manual confirmation integrates properly with existing system")
        print("   ✅ Customer contact details formatting handles all scenarios")
        print("   ✅ Edge cases and error conditions handled gracefully")
        print("   ✅ Firebase integration working correctly")
        print("   ✅ Order status transitions work as expected")
        return True
    else:
        print("⚠️ Some integration tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
