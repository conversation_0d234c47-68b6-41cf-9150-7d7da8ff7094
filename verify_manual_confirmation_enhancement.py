#!/usr/bin/env python3
"""
Simple verification for manual confirmation enhancement
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def verify_functions_exist():
    """Verify that all new functions exist and are importable"""
    print("🔍 Verifying manual confirmation enhancement functions...")
    
    try:
        from src.bots.order_track_bot import (
            send_manual_confirmation_notification_to_customer,
            get_delivery_person_info_for_notification,
            format_manual_confirmation_customer_message,
            remove_customer_confirmation_buttons
        )
        
        functions = [
            ("send_manual_confirmation_notification_to_customer", send_manual_confirmation_notification_to_customer),
            ("get_delivery_person_info_for_notification", get_delivery_person_info_for_notification),
            ("format_manual_confirmation_customer_message", format_manual_confirmation_customer_message),
            ("remove_customer_confirmation_buttons", remove_customer_confirmation_buttons)
        ]
        
        for name, func in functions:
            if callable(func):
                print(f"✅ {name} - Available and callable")
            else:
                print(f"❌ {name} - Not callable")
                return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_delivery_info_function():
    """Test delivery person info function"""
    print("\n👤 Testing delivery person info function...")
    
    try:
        from src.bots.order_track_bot import get_delivery_person_info_for_notification
        
        # Test with empty order
        empty_order = {}
        info = get_delivery_person_info_for_notification(empty_order)
        
        if info and 'name' in info and 'phone' in info:
            print(f"✅ Function returns proper structure: {info}")
            return True
        else:
            print("❌ Function doesn't return proper structure")
            return False
            
    except Exception as e:
        print(f"❌ Error testing delivery info function: {e}")
        return False

def test_message_formatting():
    """Test message formatting function"""
    print("\n📝 Testing message formatting function...")
    
    try:
        from src.bots.order_track_bot import format_manual_confirmation_customer_message
        
        test_order = {
            'restaurant_id': '1',
            'subtotal': 100,
            'delivery_fee': 20,
            'completed_at': '2024-12-30 15:00:00'
        }
        
        test_delivery_info = {
            'name': 'Test Driver',
            'phone': '+251912345678',
            'found': True
        }
        
        message = format_manual_confirmation_customer_message(
            "TEST_001",
            test_order,
            test_delivery_info
        )
        
        if message and len(message) > 100:
            print(f"✅ Message formatted successfully (length: {len(message)})")
            
            # Check for key elements
            key_elements = ["Order Completed", "TEST_001", "120 Birr", "0909782606"]
            missing = [elem for elem in key_elements if elem not in message]
            
            if not missing:
                print("✅ All key elements present in message")
                return True
            else:
                print(f"❌ Missing elements: {missing}")
                return False
        else:
            print("❌ Message formatting failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing message formatting: {e}")
        return False

def verify_integration():
    """Verify integration with existing manual confirmation handler"""
    print("\n🔗 Verifying integration with existing system...")
    
    try:
        # Check if the manual confirmation handler calls the new function
        import inspect
        from src.bots.order_track_bot import handle_manual_order_confirmation
        
        source = inspect.getsource(handle_manual_order_confirmation)
        
        if "send_manual_confirmation_notification_to_customer" in source:
            print("✅ Manual confirmation handler integrated with customer notification")
            return True
        else:
            print("❌ Manual confirmation handler not integrated")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying integration: {e}")
        return False

def main():
    """Run verification"""
    print("🔍 Manual Confirmation Enhancement Verification")
    print("=" * 50)
    
    tests = [
        verify_functions_exist,
        test_delivery_info_function,
        test_message_formatting,
        verify_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ {test.__name__} failed")
        except Exception as e:
            print(f"❌ {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Verification Results: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 Manual confirmation enhancement verified!")
        print("\n✅ Enhancement Summary:")
        print("   ✅ Customer notification functions implemented")
        print("   ✅ Button removal functionality added")
        print("   ✅ Delivery person info retrieval working")
        print("   ✅ Message formatting with contact center info")
        print("   ✅ Integration with existing manual confirmation")
        print("\n🚀 Ready for production use!")
        return True
    else:
        print("⚠️ Some verification checks failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
