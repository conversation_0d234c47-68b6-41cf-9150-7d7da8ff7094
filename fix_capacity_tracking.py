#!/usr/bin/env python3
"""
Scrip<PERSON> to fix existing capacity tracking inconsistencies caused by the bug.
This script will:
1. Recalculate capacity for all delivery personnel
2. Update capacity tracking cache
3. Ensure broadcast eligibility is correct
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import datetime

def fix_all_capacity_tracking():
    """Fix capacity tracking for all delivery personnel"""
    print("\n🔧 FIXING CAPACITY TRACKING FOR ALL PERSONNEL")
    print("=" * 60)
    
    try:
        from src.firebase_db import get_data, set_data
        from src.data_storage import load_delivery_personnel_data
        
        # Get all delivery personnel
        personnel_data = load_delivery_personnel_data()
        if not personnel_data:
            print("❌ No delivery personnel found")
            return False
        
        print(f"Found {len(personnel_data)} delivery personnel to fix")
        
        # Get confirmed orders
        confirmed_orders = get_data("confirmed_orders") or {}
        print(f"Found {len(confirmed_orders)} confirmed orders")
        
        fixed_count = 0
        
        for personnel_id, personnel_info in personnel_data.items():
            print(f"\n👤 Fixing {personnel_info.get('name')} ({personnel_id}):")
            
            # Calculate correct active orders for this personnel
            active_orders = []
            
            for order_num, order_data in confirmed_orders.items():
                if order_data.get('assigned_to') == personnel_id:
                    # Apply the fixed logic
                    status = order_data.get('status', '')
                    delivery_status = order_data.get('delivery_status', '')
                    
                    # Order is active if neither status field indicates completion
                    is_completed = (
                        status in ['completed', 'cancelled', 'delivered'] or
                        delivery_status in ['completed', 'cancelled', 'delivered']
                    )
                    
                    if not is_completed:
                        active_orders.append(order_num)
                        print(f"  🔄 Active: {order_num} (status: {status}, delivery_status: {delivery_status})")
                    else:
                        print(f"  ✅ Completed: {order_num} (status: {status}, delivery_status: {delivery_status})")
            
            correct_capacity = len(active_orders)
            print(f"  Correct capacity: {correct_capacity}")
            
            # Get current cached capacity
            cached_capacity = get_data(f"delivery_personnel_capacity_tracking/{personnel_id}")
            current_cached_count = cached_capacity.get('current_orders', 0) if cached_capacity else 0
            
            print(f"  Current cached capacity: {current_cached_count}")
            
            # Update capacity tracking if different
            if current_cached_count != correct_capacity or not cached_capacity:
                print(f"  🔧 Updating capacity: {current_cached_count} -> {correct_capacity}")
                
                # Create updated capacity data
                capacity_data = {
                    'current_orders': correct_capacity,
                    'active_order_numbers': active_orders,
                    'last_updated': datetime.datetime.now().isoformat(),
                    'fixed_at': datetime.datetime.now().isoformat(),
                    'fix_reason': 'delivery_status_bug_fix',
                    'max_capacity': personnel_info.get('max_capacity', 5)
                }
                
                # Store updated data
                success = set_data(f"delivery_personnel_capacity_tracking/{personnel_id}", capacity_data)
                
                if success:
                    print(f"  ✅ Successfully updated capacity tracking")
                    fixed_count += 1
                else:
                    print(f"  ❌ Failed to update capacity tracking")
            else:
                print(f"  ✅ Capacity tracking already correct")
        
        print(f"\n📊 SUMMARY: Fixed capacity tracking for {fixed_count} personnel")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing capacity tracking: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_broadcast_eligibility():
    """Verify that broadcast eligibility is now correct for all personnel"""
    print("\n🎯 VERIFYING BROADCAST ELIGIBILITY")
    print("=" * 60)
    
    try:
        from src.firebase_db import get_data
        from src.utils.delivery_personnel_utils import (
            find_available_personnel_with_capacity_check,
            get_real_time_capacity
        )
        from src.data_storage import load_delivery_personnel_data, load_delivery_personnel_availability_data
        
        # Get areas to test with
        areas_data = get_data("areas") or {}
        if not areas_data:
            print("❌ No areas found")
            return False
        
        # Test with first area
        test_area_id = list(areas_data.keys())[0]
        print(f"Testing broadcast eligibility for area: {test_area_id}")
        
        # Get available personnel using the fixed function
        available_personnel = find_available_personnel_with_capacity_check(test_area_id)
        print(f"Available personnel: {available_personnel}")
        
        # Check all personnel
        personnel_data = load_delivery_personnel_data()
        availability_data = load_delivery_personnel_availability_data()
        
        eligible_count = 0
        ineligible_count = 0
        
        for personnel_id, personnel_info in personnel_data.items():
            name = personnel_info.get('name', 'Unknown')
            
            # Check basic eligibility criteria
            is_verified = personnel_info.get('is_verified', False)
            status = personnel_info.get('status', 'offline')
            availability = availability_data.get(personnel_id, status)
            service_areas = personnel_info.get('service_areas', [])
            serves_area = test_area_id in service_areas
            
            # Check capacity
            capacity = get_real_time_capacity(personnel_id)
            max_capacity = personnel_info.get('max_capacity', 5)
            
            # Determine if should be eligible
            should_be_eligible = (
                is_verified and
                status != 'offline' and
                availability == 'available' and
                serves_area and
                capacity < 5
            )
            
            is_in_available_list = personnel_id in available_personnel
            
            if should_be_eligible:
                eligible_count += 1
                if is_in_available_list:
                    print(f"  ✅ {name}: Correctly eligible ({capacity}/{max_capacity} orders)")
                else:
                    print(f"  ⚠️  {name}: Should be eligible but not in list ({capacity}/{max_capacity} orders)")
                    print(f"      Verified: {is_verified}, Status: {status}, Availability: {availability}, Serves area: {serves_area}")
            else:
                ineligible_count += 1
                if not is_in_available_list:
                    print(f"  ✅ {name}: Correctly ineligible ({capacity}/{max_capacity} orders)")
                else:
                    print(f"  ⚠️  {name}: Shouldn't be eligible but is in list ({capacity}/{max_capacity} orders)")
                    print(f"      Verified: {is_verified}, Status: {status}, Availability: {availability}, Serves area: {serves_area}")
        
        print(f"\nSummary:")
        print(f"  Eligible personnel: {eligible_count}")
        print(f"  Ineligible personnel: {ineligible_count}")
        print(f"  Available for broadcast: {len(available_personnel)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying broadcast eligibility: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main fix function"""
    print("🚀 CAPACITY TRACKING FIX TOOL")
    print("=" * 60)
    
    try:
        # Step 1: Fix capacity tracking
        fix_success = fix_all_capacity_tracking()
        
        if fix_success:
            # Step 2: Verify broadcast eligibility
            verify_success = verify_broadcast_eligibility()
            
            if verify_success:
                print("\n🎉 CAPACITY TRACKING FIX COMPLETE!")
                print("✅ All delivery personnel capacity tracking has been corrected")
                print("✅ Broadcast eligibility is now working properly")
                print("\nDelivery personnel with completed orders should now be eligible")
                print("to receive new order broadcasts when their active order count is below 5.")
            else:
                print("\n⚠️  Fix applied but verification failed")
        else:
            print("\n❌ Failed to apply capacity tracking fix")
        
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
