#!/usr/bin/env python3
"""
Simple script to check Firebase state for delivery personnel and orders
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.firebase_db import get_data
    print("✅ Firebase connection successful")
    
    # Check delivery personnel
    print("\n📊 DELIVERY PERSONNEL:")
    personnel = get_data("delivery_personnel") or {}
    print(f"Found {len(personnel)} personnel")
    
    for pid, data in personnel.items():
        print(f"  {pid}: {data.get('name')} - Status: {data.get('status')} - Verified: {data.get('is_verified')}")
    
    # Check availability
    print("\n📊 AVAILABILITY:")
    availability = get_data("delivery_personnel_availability") or {}
    print(f"Found {len(availability)} availability records")
    
    for pid, status in availability.items():
        print(f"  {pid}: {status}")
    
    # Check capacity tracking
    print("\n📊 CAPACITY TRACKING:")
    for pid in personnel.keys():
        capacity_data = get_data(f"delivery_personnel_capacity_tracking/{pid}")
        if capacity_data:
            print(f"  {pid}: {capacity_data.get('current_orders', 0)} orders - {capacity_data.get('active_order_numbers', [])}")
        else:
            print(f"  {pid}: No capacity data")
    
    # Check confirmed orders
    print("\n📊 CONFIRMED ORDERS:")
    confirmed_orders = get_data("confirmed_orders") or {}
    print(f"Found {len(confirmed_orders)} confirmed orders")
    
    for order_num, order_data in confirmed_orders.items():
        assigned_to = order_data.get('assigned_to')
        status = order_data.get('status', order_data.get('delivery_status'))
        print(f"  {order_num}: Assigned to {assigned_to} - Status: {status}")
    
    # Check areas
    print("\n📊 AREAS:")
    areas = get_data("areas") or {}
    print(f"Found {len(areas)} areas")
    for area_id, area_data in list(areas.items())[:3]:  # Show first 3
        print(f"  {area_id}: {area_data.get('name', 'Unknown')}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
