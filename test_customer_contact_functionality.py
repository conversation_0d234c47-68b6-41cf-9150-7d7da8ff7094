#!/usr/bin/env python3
"""
Test Customer Contact Functionality for Order Tracking Bot
Tests the new customer contact buttons and callback handlers.
"""

import sys
import os
import datetime
import re

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_button_visibility_logic():
    """Test the button visibility logic"""
    print("🔍 Testing button visibility logic...")
    
    try:
        from src.bots.order_track_bot import should_show_customer_contact_buttons
        
        # Test case 1: Order completed but not confirmed - should show buttons
        order_data_completed = {
            'delivery_status': 'completed',
            'status': 'CONFIRMED'
        }
        
        result = should_show_customer_contact_buttons(order_data_completed)
        if result:
            print("✅ Buttons correctly shown for completed orders")
        else:
            print("❌ Buttons not shown for completed orders")
            return False
        
        # Test case 2: Order customer confirmed - should not show buttons
        order_data_confirmed = {
            'delivery_status': 'customer_confirmed',
            'status': 'CUSTOMER_CONFIRMED'
        }
        
        result = should_show_customer_contact_buttons(order_data_confirmed)
        if not result:
            print("✅ Buttons correctly hidden for confirmed orders")
        else:
            print("❌ Buttons incorrectly shown for confirmed orders")
            return False
        
        # Test case 3: Order pending assignment - should not show buttons
        order_data_pending = {
            'delivery_status': 'pending_assignment',
            'status': 'CONFIRMED'
        }
        
        result = should_show_customer_contact_buttons(order_data_pending)
        if not result:
            print("✅ Buttons correctly hidden for pending orders")
        else:
            print("❌ Buttons incorrectly shown for pending orders")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing button visibility logic: {e}")
        return False

def test_button_creation():
    """Test the button creation function"""
    print("\n🔘 Testing button creation...")
    
    try:
        from src.bots.order_track_bot import create_customer_contact_buttons
        
        order_number = "TEST_12345_20241230_001"
        markup = create_customer_contact_buttons(order_number)
        
        if markup and hasattr(markup, 'keyboard'):
            buttons = markup.keyboard
            if len(buttons) > 0 and len(buttons[0]) == 2:
                confirm_btn = buttons[0][0]
                contact_btn = buttons[0][1]
                
                # Check button texts
                if "Confirm Order Received" in confirm_btn.text and "Contact Customer" in contact_btn.text:
                    print("✅ Button texts are correct")
                else:
                    print("❌ Button texts are incorrect")
                    return False
                
                # Check callback data
                if f"manual_confirm_{order_number}" in confirm_btn.callback_data and f"contact_customer_{order_number}" in contact_btn.callback_data:
                    print("✅ Button callback data is correct")
                else:
                    print("❌ Button callback data is incorrect")
                    return False
                
                return True
            else:
                print("❌ Button layout is incorrect")
                return False
        else:
            print("❌ Markup creation failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing button creation: {e}")
        return False

def test_callback_handler_registration():
    """Test if callback handlers are properly registered"""
    print("\n📞 Testing callback handler registration...")
    
    try:
        from src.bots.order_track_bot import order_track_bot
        
        # Get all callback query handlers
        callback_handlers = []

        # Check callback_query_handlers specifically
        if hasattr(order_track_bot, 'callback_query_handlers'):
            callback_handlers.extend(order_track_bot.callback_query_handlers)

        # Also check message_handlers for callback handlers
        for handler in getattr(order_track_bot, 'message_handlers', []):
            if 'callback_query' in str(type(handler)) or 'callback' in str(handler).lower():
                callback_handlers.append(handler)
        
        print(f"Found {len(callback_handlers)} callback handlers")
        
        # Look for our specific handlers
        manual_confirm_handler_found = False
        contact_customer_handler_found = False
        
        for i, handler in enumerate(callback_handlers):
            print(f"  Handler {i+1}: {handler}")
            # Test if this handler would match our callback data
            if hasattr(handler, 'func') and handler.func:
                try:
                    # Create a mock call object to test the filter
                    class MockCall:
                        def __init__(self, data):
                            self.data = data
                    
                    test_call_confirm = MockCall('manual_confirm_12345_20241203_001')
                    test_call_contact = MockCall('contact_customer_12345_20241203_001')
                    
                    if handler.func(test_call_confirm):
                        print(f"  ✅ Handler {i+1} matches 'manual_confirm_' pattern")
                        manual_confirm_handler_found = True
                    
                    if handler.func(test_call_contact):
                        print(f"  ✅ Handler {i+1} matches 'contact_customer_' pattern")
                        contact_customer_handler_found = True
                        
                except Exception as e:
                    print(f"  ❌ Error testing handler {i+1}: {e}")
        
        if manual_confirm_handler_found and contact_customer_handler_found:
            print("✅ Customer contact callback handlers are properly registered")
            return True
        else:
            print("❌ Customer contact callback handlers NOT found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing callback handler registration: {e}")
        return False

def test_customer_contact_details_formatting():
    """Test the customer contact details formatting"""
    print("\n📋 Testing customer contact details formatting...")
    
    try:
        from src.bots.order_track_bot import format_customer_contact_details
        
        # Create test order data
        test_order_data = {
            'restaurant_id': '1',
            'phone_number': '+251912345678',
            'delivery_name': 'John Doe',
            'delivery_location': 'Test Location',
            'delivery_gate': 'Main Gate',
            'subtotal': 150,
            'delivery_fee': 25,
            'created_at': '2024-12-30 14:30:00',
            'confirmed_at': '2024-12-30 14:35:00',
            'completed_at': '2024-12-30 15:00:00',
            'assigned_to': 'test_personnel_id',
            'items': [
                {'name': 'Pizza Margherita', 'price': 120, 'quantity': 1},
                {'name': 'Coca Cola', 'price': 30, 'quantity': 1}
            ]
        }
        
        order_number = "TEST_12345_20241230_001"
        contact_message = format_customer_contact_details(order_number, test_order_data)
        
        # Check if essential information is included
        required_elements = [
            "CUSTOMER CONTACT DETAILS",
            order_number,
            test_order_data['phone_number'],
            test_order_data['delivery_name'],
            "Pizza Margherita",
            "Coca Cola",
            "150 Birr",  # subtotal
            "25 Birr",   # delivery fee
            "Customer has not confirmed receipt"
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in contact_message:
                missing_elements.append(element)
        
        if not missing_elements:
            print("✅ Customer contact details formatting is correct")
            print(f"📄 Sample message length: {len(contact_message)} characters")
            return True
        else:
            print(f"❌ Missing elements in contact details: {missing_elements}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing customer contact details formatting: {e}")
        return False

def test_firebase_integration():
    """Test Firebase integration for order status updates"""
    print("\n🔥 Testing Firebase integration...")
    
    try:
        from src.firebase_db import get_data, set_data, delete_data
        
        # Test confirmed_orders collection access
        confirmed_orders = get_data("confirmed_orders")
        print("✅ Confirmed orders collection accessible")
        
        # Create test order for manual confirmation
        test_order = {
            "order_number": "TEST_CONTACT_001",
            "user_id": "test_user",
            "status": "CONFIRMED",
            "delivery_status": "completed",
            "confirmed_at": "2024-12-30 12:00:00",
            "completed_at": "2024-12-30 12:30:00",
            "restaurant_id": "1",
            "subtotal": 150,
            "delivery_fee": 25,
            "phone_number": "+251963630623",
            "delivery_name": "Test Customer",
            "delivery_location": "Test Location"
        }
        
        # Test setting order data
        if set_data("confirmed_orders/TEST_CONTACT_001", test_order):
            print("✅ Successfully created test order for contact functionality")
            
            # Test manual confirmation update
            test_order["status"] = "CUSTOMER_CONFIRMED"
            test_order["delivery_status"] = "customer_confirmed"
            test_order["customer_confirmed_at"] = "2024-12-30 12:35:00"
            test_order["manually_confirmed_by"] = "test_admin"
            test_order["manual_confirmation"] = True
            
            if set_data("confirmed_orders/TEST_CONTACT_001", test_order):
                print("✅ Successfully updated order to manually confirmed status")
                
                # Clean up test data
                delete_data("confirmed_orders/TEST_CONTACT_001")
                print("✅ Test data cleaned up")
                return True
            else:
                print("❌ Failed to update order to manually confirmed status")
                return False
        else:
            print("❌ Failed to create test order")
            return False
        
    except Exception as e:
        print(f"❌ Error testing Firebase integration: {e}")
        return False

def main():
    """Run all customer contact functionality tests"""
    print("🚀 Starting Customer Contact Functionality Tests")
    print("=" * 60)
    
    tests = [
        test_button_visibility_logic,
        test_button_creation,
        test_callback_handler_registration,
        test_customer_contact_details_formatting,
        test_firebase_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} failed")
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All customer contact functionality tests passed!")
        return True
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
