#!/usr/bin/env python3
"""
Test Contact Customer Functionality Removal
Verifies that the contact customer functionality has been properly removed.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_removed_functions():
    """Test that removed functions are no longer available"""
    print("🔍 Testing removed functions...")
    
    try:
        from src.bots.order_track_bot import order_track_bot
        
        # Test that removed functions are not importable
        removed_functions = [
            'handle_customer_contact_request',
            'format_customer_contact_details',
            'create_customer_contact_buttons'
        ]
        
        for func_name in removed_functions:
            try:
                from src.bots import order_track_bot
                if hasattr(order_track_bot, func_name):
                    print(f"❌ Function {func_name} still exists")
                    return False
                else:
                    print(f"✅ Function {func_name} successfully removed")
            except ImportError:
                print(f"✅ Function {func_name} not found (expected)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing removed functions: {e}")
        return False

def test_renamed_functions():
    """Test that functions were properly renamed"""
    print("\n🔄 Testing renamed functions...")
    
    try:
        from src.bots.order_track_bot import (
            should_show_manual_confirmation_button,
            create_manual_confirmation_button
        )
        
        # Test that new functions exist and are callable
        new_functions = [
            ("should_show_manual_confirmation_button", should_show_manual_confirmation_button),
            ("create_manual_confirmation_button", create_manual_confirmation_button)
        ]
        
        for name, func in new_functions:
            if callable(func):
                print(f"✅ {name} - Available and callable")
            else:
                print(f"❌ {name} - Not callable")
                return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error for renamed functions: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing renamed functions: {e}")
        return False

def test_button_creation():
    """Test that only manual confirmation button is created"""
    print("\n🔘 Testing button creation...")
    
    try:
        from src.bots.order_track_bot import create_manual_confirmation_button
        
        order_number = "TEST_12345_20241230_001"
        markup = create_manual_confirmation_button(order_number)
        
        if markup and hasattr(markup, 'keyboard'):
            buttons = markup.keyboard
            if len(buttons) > 0 and len(buttons[0]) == 1:  # Should only have 1 button now
                confirm_btn = buttons[0][0]
                
                # Check button text and callback data
                if ("Confirm Order Received" in confirm_btn.text and 
                    f"manual_confirm_{order_number}" in confirm_btn.callback_data):
                    print("✅ Manual confirmation button created correctly")
                    print(f"   Button text: {confirm_btn.text}")
                    print(f"   Callback data: {confirm_btn.callback_data}")
                    
                    # Verify no contact customer button
                    if "Contact Customer" not in confirm_btn.text:
                        print("✅ Contact Customer button successfully removed")
                        return True
                    else:
                        print("❌ Contact Customer button still present")
                        return False
                else:
                    print("❌ Button creation failed - incorrect text or callback data")
                    return False
            else:
                print(f"❌ Button creation failed - incorrect number of buttons: {len(buttons[0]) if buttons else 0}")
                return False
        else:
            print("❌ Button creation failed - no markup created")
            return False
        
    except Exception as e:
        print(f"❌ Error testing button creation: {e}")
        return False

def test_callback_handler():
    """Test that callback handler only handles manual confirmation"""
    print("\n📞 Testing callback handler...")
    
    try:
        import inspect
        from src.bots.order_track_bot import handle_tracking_bot_callbacks
        
        # Get the source code of the callback handler
        source = inspect.getsource(handle_tracking_bot_callbacks)
        
        # Check that it only handles manual_confirm_ callbacks
        if "manual_confirm_" in source:
            print("✅ Callback handler supports manual confirmation")
        else:
            print("❌ Callback handler missing manual confirmation support")
            return False
        
        # Check that contact_customer_ support is removed
        if "contact_customer_" not in source:
            print("✅ Contact customer callback support successfully removed")
        else:
            print("❌ Contact customer callback support still present")
            return False
        
        # Check that the decorator only listens for manual_confirm_
        if "func=lambda call: call.data.startswith('manual_confirm_')" in source:
            print("✅ Callback handler decorator correctly updated")
        else:
            print("❌ Callback handler decorator not properly updated")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing callback handler: {e}")
        return False

def test_button_visibility_logic():
    """Test that button visibility logic still works correctly"""
    print("\n👁️ Testing button visibility logic...")
    
    try:
        from src.bots.order_track_bot import should_show_manual_confirmation_button
        
        # Test case 1: Order completed but not confirmed - should show button
        order_data_completed = {
            'delivery_status': 'completed',
            'status': 'CONFIRMED'
        }
        
        result = should_show_manual_confirmation_button(order_data_completed)
        if result:
            print("✅ Button correctly shown for completed orders")
        else:
            print("❌ Button not shown for completed orders")
            return False
        
        # Test case 2: Order customer confirmed - should not show button
        order_data_confirmed = {
            'delivery_status': 'customer_confirmed',
            'status': 'CUSTOMER_CONFIRMED'
        }
        
        result = should_show_manual_confirmation_button(order_data_confirmed)
        if not result:
            print("✅ Button correctly hidden for confirmed orders")
        else:
            print("❌ Button incorrectly shown for confirmed orders")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing button visibility logic: {e}")
        return False

def test_manual_confirmation_workflow():
    """Test that manual confirmation workflow still works"""
    print("\n🔄 Testing manual confirmation workflow...")
    
    try:
        from src.bots.order_track_bot import (
            handle_manual_order_confirmation,
            send_manual_confirmation_notification_to_customer
        )
        
        # Check that manual confirmation functions still exist
        functions_to_check = [
            ("handle_manual_order_confirmation", handle_manual_order_confirmation),
            ("send_manual_confirmation_notification_to_customer", send_manual_confirmation_notification_to_customer)
        ]
        
        for name, func in functions_to_check:
            if callable(func):
                print(f"✅ {name} - Available and callable")
            else:
                print(f"❌ {name} - Not callable")
                return False
        
        print("✅ Manual confirmation workflow functions intact")
        return True
        
    except ImportError as e:
        print(f"❌ Import error for manual confirmation functions: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing manual confirmation workflow: {e}")
        return False

def main():
    """Run all contact customer removal tests"""
    print("🧪 Contact Customer Functionality Removal Tests")
    print("=" * 60)
    
    tests = [
        test_removed_functions,
        test_renamed_functions,
        test_button_creation,
        test_callback_handler,
        test_button_visibility_logic,
        test_manual_confirmation_workflow
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} failed")
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All contact customer removal tests passed!")
        print("\n✅ Removal Summary:")
        print("   ✅ Contact Customer button removed")
        print("   ✅ Customer contact details function removed")
        print("   ✅ Contact customer callback handler removed")
        print("   ✅ Functions properly renamed")
        print("   ✅ Manual confirmation workflow preserved")
        print("   ✅ Button visibility logic working")
        print("\n🚀 Simplified customer contact functionality ready!")
        return True
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
