#!/usr/bin/env python3
"""
Test script to verify the broadcast system fix.
This script will:
1. Check if completed orders are properly excluded from capacity counting
2. Test the broadcast eligibility logic
3. Simulate order completion and verify capacity updates
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import datetime
import json

def test_capacity_calculation_fix():
    """Test that completed orders are properly excluded from capacity calculation"""
    print("\n🧪 TESTING CAPACITY CALCULATION FIX")
    print("=" * 60)
    
    try:
        from src.firebase_db import get_data, set_data
        from src.utils.delivery_personnel_utils import get_real_time_capacity
        
        # Get a sample personnel ID
        personnel_data = get_data("delivery_personnel") or {}
        if not personnel_data:
            print("❌ No delivery personnel found in database")
            return False
        
        test_personnel_id = list(personnel_data.keys())[0]
        personnel_info = personnel_data[test_personnel_id]
        print(f"Testing with personnel: {personnel_info.get('name')} ({test_personnel_id})")
        
        # Check current capacity
        current_capacity = get_real_time_capacity(test_personnel_id)
        print(f"Current capacity: {current_capacity}")
        
        # Check confirmed orders for this personnel
        confirmed_orders = get_data("confirmed_orders") or {}
        personnel_orders = []
        
        for order_num, order_data in confirmed_orders.items():
            if order_data.get('assigned_to') == test_personnel_id:
                status = order_data.get('status', '')
                delivery_status = order_data.get('delivery_status', '')
                personnel_orders.append({
                    'order_number': order_num,
                    'status': status,
                    'delivery_status': delivery_status,
                    'assigned_at': order_data.get('assigned_at', 'Unknown')
                })
        
        print(f"\nFound {len(personnel_orders)} orders assigned to this personnel:")
        
        active_count = 0
        completed_count = 0
        
        for order in personnel_orders:
            status = order['status']
            delivery_status = order['delivery_status']
            
            # Apply the same logic as the fixed function
            is_completed = (
                status in ['completed', 'cancelled', 'delivered'] or
                delivery_status in ['completed', 'cancelled', 'delivered']
            )
            
            if is_completed:
                completed_count += 1
                print(f"  ✅ {order['order_number']}: COMPLETED (status: {status}, delivery_status: {delivery_status})")
            else:
                active_count += 1
                print(f"  🔄 {order['order_number']}: ACTIVE (status: {status}, delivery_status: {delivery_status})")
        
        print(f"\nSummary:")
        print(f"  Active orders: {active_count}")
        print(f"  Completed orders: {completed_count}")
        print(f"  Real-time capacity: {current_capacity}")
        
        if active_count == current_capacity:
            print("✅ PASS: Real-time capacity matches active order count")
            return True
        else:
            print(f"❌ FAIL: Real-time capacity ({current_capacity}) doesn't match active orders ({active_count})")
            return False
            
    except Exception as e:
        print(f"❌ Error testing capacity calculation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_broadcast_eligibility_fix():
    """Test that personnel with completed orders are eligible for new broadcasts"""
    print("\n🎯 TESTING BROADCAST ELIGIBILITY FIX")
    print("=" * 60)
    
    try:
        from src.utils.delivery_personnel_utils import find_available_personnel_with_capacity_check
        from src.firebase_db import get_data
        
        # Get areas to test with
        areas_data = get_data("areas") or {}
        if not areas_data:
            print("❌ No areas found in database")
            return False
        
        test_area_id = list(areas_data.keys())[0]
        print(f"Testing with area: {test_area_id}")
        
        # Find available personnel
        available_personnel = find_available_personnel_with_capacity_check(test_area_id)
        print(f"Available personnel for broadcasts: {available_personnel}")
        
        # Check each personnel's details
        personnel_data = get_data("delivery_personnel") or {}
        
        for personnel_id in personnel_data.keys():
            personnel_info = personnel_data[personnel_id]
            print(f"\n👤 {personnel_info.get('name')} ({personnel_id}):")
            
            # Check if they serve this area
            service_areas = personnel_info.get('service_areas', [])
            serves_area = test_area_id in service_areas
            print(f"  Serves area {test_area_id}: {serves_area}")
            
            if not serves_area:
                print(f"  ❌ Skipped: Doesn't serve this area")
                continue
            
            # Check verification and status
            is_verified = personnel_info.get('is_verified', False)
            status = personnel_info.get('status', 'offline')
            print(f"  Verified: {is_verified}, Status: {status}")
            
            if not is_verified or status == 'offline':
                print(f"  ❌ Skipped: Not verified or offline")
                continue
            
            # Check capacity
            from src.utils.delivery_personnel_utils import get_real_time_capacity
            capacity = get_real_time_capacity(personnel_id)
            max_capacity = personnel_info.get('max_capacity', 5)
            print(f"  Capacity: {capacity}/{max_capacity}")
            
            # Check if they should be eligible
            should_be_eligible = capacity < 5
            is_in_list = personnel_id in available_personnel
            
            print(f"  Should be eligible: {should_be_eligible}")
            print(f"  Is in available list: {is_in_list}")
            
            if should_be_eligible and not is_in_list:
                print(f"  ⚠️  POTENTIAL ISSUE: Should be eligible but not in list")
            elif not should_be_eligible and is_in_list:
                print(f"  ⚠️  POTENTIAL ISSUE: Shouldn't be eligible but is in list")
            else:
                print(f"  ✅ Status correct")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing broadcast eligibility: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_order_completion():
    """Simulate completing an order and verify capacity updates"""
    print("\n🔄 SIMULATING ORDER COMPLETION")
    print("=" * 60)
    
    try:
        from src.firebase_db import get_data, set_data
        from src.utils.delivery_personnel_utils import (
            get_real_time_capacity,
            update_personnel_capacity_on_assignment
        )
        
        # Find an active order to complete
        confirmed_orders = get_data("confirmed_orders") or {}
        active_order = None
        personnel_id = None
        
        for order_num, order_data in confirmed_orders.items():
            if (order_data.get('assigned_to') and 
                order_data.get('delivery_status') not in ['completed', 'cancelled', 'delivered']):
                active_order = order_num
                personnel_id = order_data['assigned_to']
                break
        
        if not active_order:
            print("ℹ️  No active orders found to test completion")
            return True
        
        print(f"Testing with order: {active_order}")
        print(f"Assigned to personnel: {personnel_id}")
        
        # Check capacity before completion
        capacity_before = get_real_time_capacity(personnel_id)
        print(f"Capacity before completion: {capacity_before}")
        
        # Simulate marking order as completed (without actually doing it)
        print("\n🧪 Simulating order completion...")
        
        # Test the capacity update function
        success = update_personnel_capacity_on_assignment(personnel_id, active_order, 'complete')
        
        if success:
            # Check capacity after
            capacity_after = get_real_time_capacity(personnel_id)
            print(f"Capacity after completion: {capacity_after}")
            
            if capacity_after == capacity_before - 1:
                print("✅ PASS: Capacity correctly decreased by 1")
                
                # Restore the capacity for testing purposes
                update_personnel_capacity_on_assignment(personnel_id, active_order, 'assign')
                print("🔄 Restored original capacity for testing")
                return True
            else:
                print(f"❌ FAIL: Expected capacity {capacity_before - 1}, got {capacity_after}")
                return False
        else:
            print("❌ FAIL: Failed to update capacity")
            return False
            
    except Exception as e:
        print(f"❌ Error simulating order completion: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 BROADCAST SYSTEM FIX VERIFICATION")
    print("=" * 60)
    
    try:
        # Test 1: Capacity calculation fix
        test1_passed = test_capacity_calculation_fix()
        
        # Test 2: Broadcast eligibility fix
        test2_passed = test_broadcast_eligibility_fix()
        
        # Test 3: Order completion simulation
        test3_passed = simulate_order_completion()
        
        # Summary
        print("\n📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Capacity calculation fix: {'✅ PASS' if test1_passed else '❌ FAIL'}")
        print(f"Broadcast eligibility fix: {'✅ PASS' if test2_passed else '❌ FAIL'}")
        print(f"Order completion simulation: {'✅ PASS' if test3_passed else '❌ FAIL'}")
        
        if all([test1_passed, test2_passed, test3_passed]):
            print("\n🎉 ALL TESTS PASSED - Broadcast system fix is working!")
        else:
            print("\n⚠️  Some tests failed - further investigation needed")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
