#!/usr/bin/env python3
"""
Simple verification that contact customer functionality was removed
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def verify_removal():
    """Verify contact customer functionality removal"""
    print("🔍 Verifying Contact Customer Functionality Removal")
    print("=" * 50)
    
    verification_results = []
    
    # Check 1: New function imports
    print("📦 Checking new function imports...")
    try:
        from src.bots.order_track_bot import (
            should_show_manual_confirmation_button,
            create_manual_confirmation_button
        )
        verification_results.append("✅ New functions imported successfully")
    except ImportError as e:
        verification_results.append(f"❌ Import error: {e}")
        return False
    
    # Check 2: Removed functions are not available
    print("🗑️ Checking removed functions...")
    try:
        from src.bots import order_track_bot
        
        removed_functions = [
            'handle_customer_contact_request',
            'format_customer_contact_details',
            'create_customer_contact_buttons'
        ]
        
        all_removed = True
        for func_name in removed_functions:
            if hasattr(order_track_bot, func_name):
                verification_results.append(f"❌ Function {func_name} still exists")
                all_removed = False
            else:
                verification_results.append(f"✅ Function {func_name} successfully removed")
        
        if not all_removed:
            return False
            
    except Exception as e:
        verification_results.append(f"❌ Error checking removed functions: {e}")
        return False
    
    # Check 3: Button creation
    print("🔘 Testing button creation...")
    try:
        from src.bots.order_track_bot import create_manual_confirmation_button
        
        markup = create_manual_confirmation_button("TEST_001")
        if markup and hasattr(markup, 'keyboard') and len(markup.keyboard) > 0:
            buttons = markup.keyboard[0]
            if len(buttons) == 1:  # Should only have 1 button now
                confirm_btn = buttons[0]
                if ("Confirm Order Received" in confirm_btn.text and 
                    "manual_confirm_TEST_001" in confirm_btn.callback_data):
                    verification_results.append("✅ Single manual confirmation button created correctly")
                else:
                    verification_results.append("❌ Button creation failed: incorrect text or callback data")
                    return False
            else:
                verification_results.append(f"❌ Button creation failed: expected 1 button, got {len(buttons)}")
                return False
        else:
            verification_results.append("❌ Button creation failed: no markup created")
            return False
    except Exception as e:
        verification_results.append(f"❌ Button creation test failed: {e}")
        return False
    
    # Check 4: Button visibility logic
    print("👁️ Testing button visibility logic...")
    try:
        from src.bots.order_track_bot import should_show_manual_confirmation_button
        
        # Test completed order
        completed_order = {'delivery_status': 'completed', 'status': 'CONFIRMED'}
        should_show = should_show_manual_confirmation_button(completed_order)
        
        # Test confirmed order
        confirmed_order = {'delivery_status': 'customer_confirmed', 'status': 'CUSTOMER_CONFIRMED'}
        should_hide = should_show_manual_confirmation_button(confirmed_order)
        
        if should_show and not should_hide:
            verification_results.append("✅ Button visibility logic working correctly")
        else:
            verification_results.append(f"❌ Button visibility logic failed: show={should_show}, hide={should_hide}")
            return False
    except Exception as e:
        verification_results.append(f"❌ Button visibility test failed: {e}")
        return False
    
    # Check 5: Manual confirmation workflow still intact
    print("🔄 Checking manual confirmation workflow...")
    try:
        from src.bots.order_track_bot import (
            handle_manual_order_confirmation,
            send_manual_confirmation_notification_to_customer
        )
        
        if callable(handle_manual_order_confirmation) and callable(send_manual_confirmation_notification_to_customer):
            verification_results.append("✅ Manual confirmation workflow functions intact")
        else:
            verification_results.append("❌ Manual confirmation workflow functions not callable")
            return False
    except ImportError as e:
        verification_results.append(f"❌ Manual confirmation workflow import error: {e}")
        return False
    
    # Print results
    print("\n📊 Verification Results:")
    print("-" * 50)
    for result in verification_results:
        print(result)
    
    # Count successful checks
    successful = sum(1 for result in verification_results if result.startswith("✅"))
    total = len([r for r in verification_results if r.startswith(("✅", "❌"))])
    
    print(f"\n📈 Success Rate: {successful}/{total} checks passed")
    
    if successful == total:
        print("\n🎉 All verification checks passed!")
        print("🚀 Contact customer functionality successfully removed!")
        print("\n📋 Summary of changes:")
        print("   ✅ Contact Customer button removed")
        print("   ✅ Customer contact details function removed")
        print("   ✅ Contact customer callback handler removed")
        print("   ✅ Functions renamed appropriately")
        print("   ✅ Manual confirmation workflow preserved")
        print("   ✅ Only single confirmation button remains")
        return True
    else:
        print("\n⚠️ Some verification checks failed.")
        return False

def main():
    """Run verification"""
    try:
        return verify_removal()
    except Exception as e:
        print(f"❌ Verification failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
