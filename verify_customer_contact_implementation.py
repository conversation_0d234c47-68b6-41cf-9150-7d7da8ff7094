#!/usr/bin/env python3
"""
Verification Script for Customer Contact Implementation
Quick verification that all components are properly implemented.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def verify_implementation():
    """Verify that all customer contact functionality is properly implemented"""
    print("🔍 Verifying Customer Contact Implementation")
    print("=" * 50)
    
    verification_results = []
    
    # Check 1: Function imports
    print("📦 Checking function imports...")
    try:
        from src.bots.order_track_bot import (
            should_show_customer_contact_buttons,
            create_customer_contact_buttons,
            format_customer_contact_details,
            handle_manual_order_confirmation,
            handle_customer_contact_request,
            process_customer_confirmation
        )
        verification_results.append("✅ All new functions imported successfully")
    except ImportError as e:
        verification_results.append(f"❌ Import error: {e}")
        return False
    
    # Check 2: Button visibility logic
    print("🔘 Testing button visibility logic...")
    try:
        # Test completed order
        completed_order = {'delivery_status': 'completed', 'status': 'CONFIRMED'}
        should_show = should_show_customer_contact_buttons(completed_order)
        
        # Test confirmed order
        confirmed_order = {'delivery_status': 'customer_confirmed', 'status': 'CUSTOMER_CONFIRMED'}
        should_hide = should_show_customer_contact_buttons(confirmed_order)
        
        if should_show and not should_hide:
            verification_results.append("✅ Button visibility logic working correctly")
        else:
            verification_results.append(f"❌ Button visibility logic failed: show={should_show}, hide={should_hide}")
            return False
    except Exception as e:
        verification_results.append(f"❌ Button visibility test failed: {e}")
        return False
    
    # Check 3: Button creation
    print("🔲 Testing button creation...")
    try:
        markup = create_customer_contact_buttons("TEST_001")
        if markup and hasattr(markup, 'keyboard') and len(markup.keyboard) > 0:
            buttons = markup.keyboard[0]
            if len(buttons) == 2:
                confirm_btn = buttons[0]
                contact_btn = buttons[1]
                if ("Confirm Order Received" in confirm_btn.text and 
                    "Contact Customer" in contact_btn.text and
                    "manual_confirm_TEST_001" in confirm_btn.callback_data and
                    "contact_customer_TEST_001" in contact_btn.callback_data):
                    verification_results.append("✅ Button creation working correctly")
                else:
                    verification_results.append("❌ Button creation failed: incorrect text or callback data")
                    return False
            else:
                verification_results.append("❌ Button creation failed: incorrect number of buttons")
                return False
        else:
            verification_results.append("❌ Button creation failed: no markup created")
            return False
    except Exception as e:
        verification_results.append(f"❌ Button creation test failed: {e}")
        return False
    
    # Check 4: Contact details formatting
    print("📋 Testing contact details formatting...")
    try:
        test_order = {
            'phone_number': '+251912345678',
            'delivery_name': 'Test Customer',
            'delivery_location': 'Test Location',
            'subtotal': 100,
            'delivery_fee': 20,
            'items': [{'name': 'Test Item', 'price': 100, 'quantity': 1}]
        }
        contact_details = format_customer_contact_details("TEST_002", test_order)
        
        required_elements = [
            "CUSTOMER CONTACT DETAILS",
            "TEST_002",
            "+251912345678",
            "Test Customer",
            "Test Item",
            "Customer has not confirmed receipt"
        ]
        
        missing = [elem for elem in required_elements if elem not in contact_details]
        if not missing:
            verification_results.append("✅ Contact details formatting working correctly")
        else:
            verification_results.append(f"❌ Contact details formatting failed: missing {missing}")
            return False
    except Exception as e:
        verification_results.append(f"❌ Contact details formatting test failed: {e}")
        return False
    
    # Check 5: Firebase integration
    print("🔥 Testing Firebase integration...")
    try:
        from src.firebase_db import set_data, get_data, delete_data
        
        test_data = {"test": "customer_contact_verification"}
        if set_data("test_customer_contact", test_data):
            retrieved = get_data("test_customer_contact")
            if retrieved and retrieved.get("test") == "customer_contact_verification":
                delete_data("test_customer_contact")
                verification_results.append("✅ Firebase integration working correctly")
            else:
                verification_results.append("❌ Firebase integration failed: data retrieval issue")
                return False
        else:
            verification_results.append("❌ Firebase integration failed: data storage issue")
            return False
    except Exception as e:
        verification_results.append(f"❌ Firebase integration test failed: {e}")
        return False
    
    # Check 6: Callback handler registration
    print("📞 Checking callback handler registration...")
    try:
        from src.bots.order_track_bot import order_track_bot
        
        # Check if callback_query_handlers exist
        if hasattr(order_track_bot, 'callback_query_handlers'):
            handlers = order_track_bot.callback_query_handlers
            if len(handlers) > 0:
                verification_results.append("✅ Callback handlers registered")
            else:
                verification_results.append("⚠️ No callback handlers found (may be normal)")
        else:
            verification_results.append("⚠️ No callback_query_handlers attribute (may be normal)")
    except Exception as e:
        verification_results.append(f"⚠️ Callback handler check failed: {e}")
    
    # Print results
    print("\n📊 Verification Results:")
    print("-" * 50)
    for result in verification_results:
        print(result)
    
    # Count successful checks
    successful = sum(1 for result in verification_results if result.startswith("✅"))
    total = len([r for r in verification_results if r.startswith(("✅", "❌"))])
    
    print(f"\n📈 Success Rate: {successful}/{total} checks passed")
    
    if successful == total:
        print("\n🎉 All verification checks passed!")
        print("🚀 Customer contact functionality is properly implemented and ready for use!")
        return True
    else:
        print("\n⚠️ Some verification checks failed.")
        return False

def main():
    """Run verification"""
    try:
        return verify_implementation()
    except Exception as e:
        print(f"❌ Verification failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
