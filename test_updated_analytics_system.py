#!/usr/bin/env python3
"""
Test script for the updated analytics system with proper order status transitions and time-based resets.

Tests:
1. Order categorization with issue handling
2. Time-based reset functionality
3. Financial calculations accuracy
4. Issue resolution tracking
5. Analytics integration
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_order_categorization_with_issues():
    """Test the updated order categorization logic"""
    print("\n📊 TESTING ORDER CATEGORIZATION WITH ISSUES")
    print("=" * 60)
    
    try:
        from src.bots.management_bot import categorize_orders_by_status, calculate_category_percentages
        
        # Create test data with various order statuses
        completed_orders = {
            'order_1': {
                'order_number': 'order_1',
                'delivery_status': 'customer_confirmed',
                'completed_at': '2024-01-15 10:00:00'
            },
            'order_2': {
                'order_number': 'order_2',
                'delivery_status': 'customer_confirmed',
                'completed_at': '2024-01-15 11:00:00',
                'issue_reported_at': '2024-01-15 09:30:00',  # Had issue but resolved
                'issue_finally_resolved_at': '2024-01-15 10:45:00'
            }
        }
        
        confirmed_orders = {
            'order_3': {
                'order_number': 'order_3',
                'delivery_status': 'assigned',
                'confirmed_at': '2024-01-15 12:00:00'
            },
            'order_4': {
                'order_number': 'order_4',
                'delivery_status': 'delivery_issue',
                'confirmed_at': '2024-01-15 13:00:00',
                'issue_reported_at': '2024-01-15 13:30:00'
            },
            'order_5': {
                'order_number': 'order_5',
                'delivery_status': 'completed',
                'confirmed_at': '2024-01-15 14:00:00',
                'completed_at': '2024-01-15 14:30:00'
            }
        }
        
        assignments_data = {}
        
        # Test categorization
        categorized = categorize_orders_by_status(completed_orders, confirmed_orders, assignments_data)
        stats = calculate_category_percentages(categorized)
        
        print(f"✅ Categorization completed successfully")
        print(f"   Complete orders: {len(categorized['complete'])}")
        print(f"   Incomplete orders: {len(categorized['incomplete'])}")
        print(f"   Issue reported orders: {len(categorized['issue_reported'])}")
        print(f"   Total count: {stats['total_count']}")
        
        # Verify logic: order_4 should be in both incomplete and issue_reported
        order_4_in_incomplete = any(o.get('order_number') == 'order_4' for o in categorized['incomplete'])
        order_4_in_issues = any(o.get('order_number') == 'order_4' for o in categorized['issue_reported'])
        
        if order_4_in_incomplete and order_4_in_issues:
            print("✅ Issue order correctly appears in both incomplete and issue categories")
        else:
            print("❌ Issue order categorization failed")
            return False
            
        # Verify completed order with resolved issue
        order_2_in_complete = any(o.get('order_number') == 'order_2' for o in categorized['complete'])
        order_2_in_issues = any(o.get('order_number') == 'order_2' for o in categorized['issue_reported'])
        
        if order_2_in_complete and order_2_in_issues:
            print("✅ Resolved issue order correctly appears in both complete and issue categories")
        else:
            print("❌ Resolved issue order categorization failed")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing order categorization: {e}")
        return False

def test_time_based_reset_system():
    """Test the time-based reset system"""
    print("\n🔄 TESTING TIME-BASED RESET SYSTEM")
    print("=" * 60)
    
    try:
        from src.utils.time_based_reset_utils import (
            get_current_time_periods,
            should_reset_daily,
            should_reset_weekly,
            should_reset_monthly,
            get_reset_status
        )
        
        # Test time period calculation
        periods = get_current_time_periods()
        print(f"✅ Current time periods calculated:")
        print(f"   Date: {periods['current_date']}")
        print(f"   Week start: {periods['current_week_start']}")
        print(f"   Month start: {periods['current_month_start']}")
        
        # Test reset status checking
        reset_status = get_reset_status()
        print(f"✅ Reset status retrieved:")
        print(f"   Needs daily reset: {reset_status.get('needs_daily_reset', 'Unknown')}")
        print(f"   Needs weekly reset: {reset_status.get('needs_weekly_reset', 'Unknown')}")
        print(f"   Needs monthly reset: {reset_status.get('needs_monthly_reset', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing time-based reset system: {e}")
        return False

def test_financial_calculations():
    """Test that financial calculations only count completed orders"""
    print("\n💰 TESTING FINANCIAL CALCULATIONS")
    print("=" * 60)
    
    try:
        from src.bots.management_bot import categorize_orders_by_status, safe_get_numeric_value
        
        # Create test data with mixed order statuses
        completed_orders = {
            'complete_1': {
                'subtotal': 100,
                'delivery_fee': 20,
                'delivery_status': 'customer_confirmed'
            }
        }
        
        confirmed_orders = {
            'incomplete_1': {
                'subtotal': 150,
                'delivery_fee': 25,
                'delivery_status': 'assigned'
            },
            'issue_1': {
                'subtotal': 200,
                'delivery_fee': 30,
                'delivery_status': 'delivery_issue',
                'issue_reported_at': '2024-01-15 10:00:00'
            }
        }
        
        # Categorize orders
        categorized = categorize_orders_by_status(completed_orders, confirmed_orders, {})
        
        # Calculate revenue only from complete orders
        food_revenue = sum(safe_get_numeric_value(order, 'subtotal', 0) for order in categorized['complete'])
        delivery_revenue = sum(safe_get_numeric_value(order, 'delivery_fee', 0) for order in categorized['complete'])
        total_revenue = food_revenue + delivery_revenue
        
        print(f"✅ Financial calculations completed:")
        print(f"   Complete orders revenue: {total_revenue} birr")
        print(f"   Food revenue: {food_revenue} birr")
        print(f"   Delivery revenue: {delivery_revenue} birr")
        
        # Verify only completed orders are counted
        expected_total = 120  # Only from complete_1
        if total_revenue == expected_total:
            print("✅ Financial calculations correctly exclude incomplete and issue orders")
        else:
            print(f"❌ Financial calculations incorrect. Expected {expected_total}, got {total_revenue}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing financial calculations: {e}")
        return False

def test_issue_resolution_tracking():
    """Test the enhanced issue resolution tracking"""
    print("\n🚨 TESTING ISSUE RESOLUTION TRACKING")
    print("=" * 60)
    
    try:
        # Test that the tracking structures are properly defined
        test_order_data = {
            'order_number': 'test_123',
            'delivery_status': 'delivery_issue',
            'issue_reported_at': '2024-01-15 10:00:00',
            'issue_resolution_history': [
                {
                    'timestamp': '2024-01-15 10:00:00',
                    'action': 'issue_reported',
                    'reported_by': '12345',
                    'status': 'delivery_issue',
                    'description': 'Customer reported order not received'
                }
            ],
            'confirmation_attempts': 1,
            'analytics_status': 'incomplete_with_issue'
        }
        
        print("✅ Issue resolution tracking structure validated:")
        print(f"   Order status: {test_order_data['delivery_status']}")
        print(f"   Analytics status: {test_order_data['analytics_status']}")
        print(f"   Confirmation attempts: {test_order_data['confirmation_attempts']}")
        print(f"   Resolution history entries: {len(test_order_data['issue_resolution_history'])}")
        
        # Test resolution progression
        test_order_data['issue_resolution_history'].append({
            'timestamp': '2024-01-15 10:30:00',
            'action': 'issue_resolved_by_delivery',
            'resolved_by': 'delivery_001',
            'status': 'completed',
            'description': 'Delivery personnel marked order as completed after issue resolution'
        })
        
        test_order_data['analytics_status'] = 'resolved_pending_confirmation'
        
        print("✅ Issue resolution progression tracked:")
        print(f"   Updated analytics status: {test_order_data['analytics_status']}")
        print(f"   Resolution history entries: {len(test_order_data['issue_resolution_history'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing issue resolution tracking: {e}")
        return False

def test_analytics_integration():
    """Test that analytics integration works with all updates"""
    print("\n📈 TESTING ANALYTICS INTEGRATION")
    print("=" * 60)
    
    try:
        from src.bots.management_bot import refresh_analytics_data
        
        # Test analytics data refresh with time-based resets
        analytics_data = refresh_analytics_data()
        
        print("✅ Analytics data refresh completed:")
        print(f"   Completed orders: {len(analytics_data.get('completed_orders', {}))}")
        print(f"   Confirmed orders: {len(analytics_data.get('confirmed_orders', {}))}")
        print(f"   Assignments: {len(analytics_data.get('assignments', {}))}")
        print(f"   Personnel: {len(analytics_data.get('personnel', {}))}")
        
        # Test that time-based resets are integrated
        from src.utils.time_based_reset_utils import check_and_execute_time_based_resets
        reset_results = check_and_execute_time_based_resets()
        
        print("✅ Time-based reset integration tested:")
        print(f"   Reset results: {reset_results}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing analytics integration: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Updated Analytics System...\n")
    
    tests = [
        ("Order Categorization with Issues", test_order_categorization_with_issues),
        ("Time-Based Reset System", test_time_based_reset_system),
        ("Financial Calculations", test_financial_calculations),
        ("Issue Resolution Tracking", test_issue_resolution_tracking),
        ("Analytics Integration", test_analytics_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Updated analytics system is working correctly.")
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    main()
